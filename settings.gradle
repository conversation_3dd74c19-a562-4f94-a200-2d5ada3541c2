pluginManagement {
    repositories {
        maven {
            allowInsecureProtocol true
            url '/Users/<USER>/Desktop/AndroidProject/pins-plugin/maven'
        }
        maven {
            allowInsecureProtocol true
            url 'http://android.internal.taqu.cn:8081/repository/android-public/'
        }
        gradlePluginPortal()
        google()
        mavenCentral()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        maven {
            allowInsecureProtocol true
            url 'http://android.internal.taqu.cn:8081/repository/android-public/'
        }
        google()
        mavenCentral()
    }
}
rootProject.name = "My Application"
include ':app'
include ':jvmti-native'
include ':module_family'
//include ':pins-plugin'
//include ':extends'
//include ':module_family:module_game'

//project(':module_game').projectDir = file("${project(':module_family').projectDir}/module_game")
include ':compose-test'
