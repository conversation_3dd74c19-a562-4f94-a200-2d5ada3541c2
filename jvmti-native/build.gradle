plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
}

android {
    compileSdk 32

    defaultConfig {
        minSdk 21
        targetSdk 32

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
        externalNativeBuild {
            cmake {
                cppFlags ""
            }
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    externalNativeBuild {
        cmake {
            path "src/main/cpp/CMakeLists.txt"
            version "3.18.1"
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
}

dependencies {

    implementation 'androidx.core:core-ktx:1.3.2'
    implementation 'androidx.appcompat:appcompat:1.4.2'
    implementation 'com.google.android.material:material:1.6.1'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.4'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.0'
}


apply plugin: 'maven-publish'

group 'hb'
version '0.0.1-alpha02'

//publishing {
//    publications {
//        release(MavenPublication) {
////            from components.release
//            afterEvaluate { artifact(tasks.getByName("bundleReleaseAar")) }
//            groupId = group
//            artifactId = 'XJvmti'
//            version = version
//            pom.withXml {
//
////                def dependencyNode2 = dependenciesNode.appendNode('dependency')
////                dependencyNode2.appendNode('groupId', 'com.github.tiann')
////                dependencyNode2.appendNode('artifactId', 'FreeReflection')
////                dependencyNode2.appendNode('version', '3.1.0')
//
//            }
//        }
//    }
//    repositories {
//        maven {
//            url "http://android.internal.taqu.cn:8081/repository/maven-releases/"
//            allowInsecureProtocol true
//            credentials {
//                username = "android"
//                password = "android"
//            }
//        }
//    }
//
//    task androidSourcesJar(type: Jar) {
//        classifier = 'sources'
//        from android.sourceSets.main.java.srcDirs
//    }
//
//    artifacts {
//        archives androidSourcesJar
//    }
//
//}