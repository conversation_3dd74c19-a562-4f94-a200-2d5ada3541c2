#include <jni.h>
#include <string>
#include <map>

#include "jvmti.h"
#include <android/log.h>


#define LOG_TAG "jvmti"
#define ALOGV(...) __android_log_print(ANDROID_LOG_VERBOSE, LOG_TAG, __VA_ARGS__)
#define ALOGD(...) __android_log_print(ANDROID_LOG_DEBUG, LOG_TAG, __VA_ARGS__)
#define ALOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define ALOGW(...) __android_log_print(ANDROID_LOG_WARN, LOG_TAG, __VA_ARGS__)
#define ALOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)


static jrawMonitorID lock;
jvmtiEnv *mJvmtiEnv;

// 记录耗时 map
std::map<jclass, jlong> classLoadTime;


jvmtiEnv *CreateJvmtiEnv(JavaVM *vm) {
    jvmtiEnv *jvmti_env;
    jint result = vm->GetEnv((void **) &jvmti_env, JVMTI_VERSION_1_2);
    if (result != JNI_OK) {
        ALOGI ("CreateJvmtiEnv is NULL");
        return nullptr;
    }
    return jvmti_env;
}

void JNICALL ClassLoad(jvmtiEnv *jvmti_env,
                       JNIEnv *jni_env,
                       jthread thread,
                       jclass klass) {

    jvmti_env->GetTime(&classLoadTime[klass]);
}

/**
 * class 加载完毕
 * @param jvmti_env
 * @param jni_env
 * @param thread
 * @param klass
 */
void JNICALL
ClassPrepare(jvmtiEnv *jvmti_env,
             JNIEnv* jni_env,
             jthread thread,
             jclass klass) {
    jlong endTime;
    jvmti_env->GetTime(&endTime);
    jlong startTime = classLoadTime[klass];
    jlong duration = endTime - startTime;
    // 移除 map 中的 class
    classLoadTime.erase(klass);


    // 处理加载耗时..

    char *className;
    jvmtiError error;
    error = (*jvmti_env).GetClassSignature(klass, &className, nullptr);

    if (error != JVMTI_ERROR_NONE) {
        ALOGI("Error in getting class name %d\n", error);
        return;
    }


    ALOGI("类加载: %s 耗时: %d ms -- size:%d" ,className,(int )duration/1000000,classLoadTime.size());


    // Release memory
    error = (*jvmti_env).Deallocate((unsigned char *) className);

    if (error != JVMTI_ERROR_NONE) {
        ALOGI("Error in deallocating class name %d\n", error);
    }
}


//初始化工作
extern "C"
JNIEXPORT jint JNICALL
Agent_OnAttach(JavaVM *vm, char *options, void *reserved) {
    int error;
    //准备JVMTI环境
    mJvmtiEnv = CreateJvmtiEnv(vm);

    //开启JVMTI的能力
    jvmtiCapabilities caps;
    mJvmtiEnv->GetPotentialCapabilities(&caps);
    mJvmtiEnv->AddCapabilities(&caps);

    Agent_OnLoad(vm, options, reserved);

    ALOGI("Agent_OnAttach Finish");
    return JNI_OK;
}


JNIEXPORT jint JNICALL Agent_OnLoad(JavaVM *vm, char *options, void *reserved) {
    jvmtiEnv *jvmti;

    (*vm).GetEnv((void **) &jvmti, JVMTI_VERSION_1_0);


    jvmtiEventCallbacks callbacks;
    memset(&callbacks, 0, sizeof(callbacks));
    callbacks.ClassLoad = &ClassLoad;
    callbacks.ClassPrepare = &ClassPrepare;

    //设置回调函数
    int error = jvmti->SetEventCallbacks(&callbacks, sizeof(callbacks));
    ALOGI("返回码：%d\n", error);

    // 开启注册 JVMTI_EVENT_CLASS_LOAD 、 JVMTI_EVENT_CLASS_PREPARE
    jvmti->SetEventNotificationMode(JVMTI_ENABLE, JVMTI_EVENT_CLASS_LOAD,
                                              nullptr);
    jvmti->SetEventNotificationMode(JVMTI_ENABLE, JVMTI_EVENT_CLASS_PREPARE,
                                    nullptr);

    return JNI_OK;
}

JNIEXPORT void JNICALL Agent_OnUnload(JavaVM *vm) {
    // 结束挂载
}






