package hb.jvmti

import android.util.Log
import dalvik.system.PathClassLoader

/**
 * 统计loadClass耗时
 *
 * <AUTHOR>
 * @date 2023-01-29
 */
class LogClassLoader(dexPath: String, parent: ClassLoader) : PathClassLoader(dexPath, parent) {


    override fun loadClass(name: String, resolve: Boolean): Class<*> {
        val start = System.currentTimeMillis()
        try {
            return super.loadClass(name, resolve)
        } finally {
            val cost = System.currentTimeMillis() - start
            val thread = Thread.currentThread().name
            Log.i("kyluzoi", "load $name thread:$thread cost: $cost")
        }
    }
}