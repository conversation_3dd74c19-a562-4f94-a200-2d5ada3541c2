package hb.jvmti

import android.app.Application
import android.util.Log
import dalvik.system.BaseDexClassLoader

/**
 *
 *
 * <AUTHOR>
 * @date 2023-01-29
 */
object ClassLoaderHook {

    fun hook(systemClassloader: ClassLoader) {

        Log.d("kyluzoi","hook")
        try {
            val logClassLoader = LogClassLoader("", systemClassloader.parent)
            val pathListField = BaseDexClassLoader::class.java.getDeclaredField("pathList")
            pathListField.isAccessible = true
            val pathList = pathListField.get(systemClassloader)
            pathListField.set(logClassLoader, pathList)

            val parentField = ClassLoader::class.java.getDeclaredField("parent")
            parentField.isAccessible = true
            parentField.set(systemClassloader, logClassLoader)
            Log.d("kyluzoi","设置成功")

        } catch (throwable: Throwable) {
            Log.e("kyluzoi", throwable.stackTraceToString())
        }
    }

    fun setBootClassloader(){

    }

}