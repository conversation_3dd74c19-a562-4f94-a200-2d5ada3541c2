<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="hb.monitor.jvmti">

    <uses-permission android:name="android.permission.VIBRATE" />

    <application
        android:name=".MyApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.MyApplication"
        tools:targetApi="31">
        <activity
            android:name=".MainActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity android:name="hb.monitor.demo.Demo1Activity" />
        <activity android:name="hb.monitor.familytag.Demo2Activity"
            android:exported="true"/>
        <activity android:name="hb.monitor.demo.StartUpActivity" />
        <activity android:name="hb.monitor.demo.GiftPanelActivity" />
        <activity android:name="hb.monitor.demo.ViewPagerTestActivity" />
        <activity 
            android:name="hb.monitor.demo.HeartAnimationDemoActivity"
            android:exported="true" />
        <activity 
            android:name="hb.monitor.demo.HeartViewDemoActivity"
            android:exported="true" />
        <activity 
            android:name=".HeartTestActivity"
            android:exported="true" />
        <activity 
            android:name="hb.monitor.demo.HeartFlyDemoActivity"
            android:exported="true" />
        <activity 
            android:name="hb.monitor.demo.GradientTextDemoActivity"
            android:exported="true" />
        <activity android:name="hb.monitor.demo.GalleryDemoActivity" />
        <activity android:name=".PKDemoActivity" />
    </application>

</manifest>