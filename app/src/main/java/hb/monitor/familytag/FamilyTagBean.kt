package hb.monitor.familytag

import com.google.gson.annotations.SerializedName
import hb.monitor.familytag.tagstyle.FamilyTagStyle

/**
 * 家族标签信息
 *
 * <AUTHOR>
 * @date 2024-06-18
 */
class FamilyTagBean(
    /**
     * 标签ID
     */
    @SerializedName("tag_id")
    var tagId: String?,
    /**
     * 标签名称
     */
    @SerializedName("name")
    var tagName: String?,

    /**
     * tag 类型
     * 1. 特定标签 - 具有特殊意义
     * - 地区(78%福建) 、 年龄段(32%90后)、性别(80%女)、活跃人数(128人活跃)。
     * - 特定标签每个会有固定样式 取 style。
     * 2. 兴趣标签
     * 3. 平台标签(已解锁)
     * - CPDD、红包多多 等..
     * 4. 平台标签(未解锁)
     * 5. 负面标签
     */
    @SerializedName("type")
    var tagType: String?,

    /**
     * 标签样式
     */
    @SerializedName("style")
    var tagStyle: String?,

    /**
     * 标签描述文案
     */
    @SerializedName("desc")
    var desc: String?,

    /**
     * 是否选中
     */
    @SerializedName("is_selected")
    var isSelected: Boolean = false
) {
    /**
     * 真实的样式模型，解析过，不重复解析生成
     */
    var tagStyleInfo: FamilyTagStyle? = null

}