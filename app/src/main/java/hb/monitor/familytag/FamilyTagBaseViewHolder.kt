package hb.monitor.familytag

import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import hb.monitor.familytag.provider.FamilyTagStyleProvider
import hb.monitor.familytag.tagstyle.FamilyTagStyle
import hb.monitor.familytag.tagstyle.StyleBlue
import hb.monitor.familytag.tagstyle.StyleBlue800
import hb.monitor.familytag.tagstyle.StylePink
import hb.xadapter.XBaseViewHolder

/**
 * 家族标签 ViewHolder
 *
 * <AUTHOR>
 * @date 2024-06-18
 */
abstract class FamilyTagBaseViewHolder : XBaseViewHolder<FamilyTagBean> {
    var styleProvider: FamilyTagStyleProvider? = null

    constructor(viewGroup: ViewGroup, layoutId: Int) : super(viewGroup, layoutId) {
    }

    constructor(view: View) : super(view)

    override fun onBindView(item: FamilyTagBean) {
        getTagTextView().apply {
            text = item.tagName
            val style = getStyle(item, adapterPosition)
            setBackgroundDrawable(style.createTagDrawable(context))
            setTextColor(style.textColor(context))
        }
    }

    abstract fun getTagTextView(): TextView


    fun getStyle(item: FamilyTagBean, position: Int): FamilyTagStyle {
        // 优先取 item.tagStyleInfo 如果 为空 则走下面的when语句，并且赋值给 item.tagStyleInfo
        item.tagStyleInfo = item.tagStyleInfo ?: when (item.tagStyle) {
            "1" -> StyleBlue()
            "2" -> StylePink()
            else -> styleProvider?.getStyle(item, position) ?: StyleBlue800()
        }
        return item.tagStyleInfo!!
    }
}