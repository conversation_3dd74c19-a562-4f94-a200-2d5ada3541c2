package hb.monitor.familytag

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import hb.xrequest.awaitNullable
import hb.xrequest.launchHttp
import kotlinx.coroutines.flow.MutableStateFlow

/**
 *
 *
 * <AUTHOR>
 * @date 2024-06-18
 */
class FamilyTagViewModel : ViewModel() {

    val tagListFlow = MutableStateFlow<List<FamilyTagBean>>(listOf())
    val repo by lazy {
        Repo()
    }


    fun fetchTagList() {
        viewModelScope.launchHttp({
            val resp = repo.fetchTagList().awaitNullable()
            resp?.let {
                tagListFlow.value = it
            }
        }){
            Log.d("kyluzoi",it.toString())
            return@launchHttp false
        }
    }
}