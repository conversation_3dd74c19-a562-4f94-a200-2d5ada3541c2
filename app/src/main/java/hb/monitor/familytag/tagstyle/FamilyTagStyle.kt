package hb.monitor.familytag.tagstyle

import android.content.Context
import android.graphics.drawable.Drawable
import androidx.core.content.ContextCompat
import hb.drawable.shape.shape.ShapeBuilder
import hb.utils.SizeUtils

/**
 * 家族标签样式抽象
 *
 * 与服务端协定 默认的几种样式
 *
 * <AUTHOR>
 * @date 2024-06-19
 */
interface FamilyTagStyle {

    /**
     * 创建背景 drawable
     */
    fun createTagDrawable(context: Context): Drawable

    /**
     * 获取文本颜色
     */
    fun textColor(context: Context): Int
}


// region 特定标签样式
/**
 * 样式1
 * 蓝底-边框-蓝字-圆角
 * 地区: 78%福建
 */
class StyleBlue : FamilyTagStyle {
    override fun createTagDrawable(context: Context): Drawable {
        return ShapeBuilder()
            .corner(SizeUtils.dp2px(12f).toFloat())
            .solid(ContextCompat.getColor(context, hb.xstyle.R.color.TH_Blue100))
            .stroke(
                SizeUtils.dp2px(0.5f),
                ContextCompat.getColor(context, hb.xstyle.R.color.TH_Blue200)
            )
            .create()
    }

    override fun textColor(context: Context): Int {
        return ContextCompat.getColor(context, hb.xstyle.R.color.TH_Blue600)
    }

}


/**
 * 样式2
 * 紫底-边框-紫字-圆角
 * 年龄段: 32%90后
 */
class StylePurple : FamilyTagStyle {
    override fun createTagDrawable(context: Context): Drawable {
        return ShapeBuilder()
            .corner(SizeUtils.dp2px(12f).toFloat())
            .solid(ContextCompat.getColor(context, hb.xstyle.R.color.TH_Purple100))
            .stroke(
                SizeUtils.dp2px(0.5f),
                ContextCompat.getColor(context, hb.xstyle.R.color.TH_Purple200)
            )
            .create()
    }

    override fun textColor(context: Context): Int {
        return ContextCompat.getColor(context, hb.xstyle.R.color.TH_Purple600)
    }

}

/**
 * 样式3
 * 性别女: 80%女
 */
class StylePink : FamilyTagStyle {
    override fun createTagDrawable(context: Context): Drawable {
        return ShapeBuilder()
            .corner(SizeUtils.dp2px(12f).toFloat())
            .solid(ContextCompat.getColor(context, hb.xstyle.R.color.TH_Pink100))
            .stroke(
                SizeUtils.dp2px(0.5f),
                ContextCompat.getColor(context, hb.xstyle.R.color.TH_Pink200)
            )
            .create()
    }

    override fun textColor(context: Context): Int {
        return ContextCompat.getColor(context, hb.xstyle.R.color.TH_Pink600)
    }

}

/**
 * 样式4
 * 性别男: 80%男
 */
class StyleCyan : FamilyTagStyle {
    override fun createTagDrawable(context: Context): Drawable {
        return ShapeBuilder()
            .corner(SizeUtils.dp2px(12f).toFloat())
            .solid(ContextCompat.getColor(context, hb.xstyle.R.color.TH_Cyan100))
            .stroke(
                SizeUtils.dp2px(0.5f),
                ContextCompat.getColor(context, hb.xstyle.R.color.TH_Cyan200)
            )
            .create()
    }

    override fun textColor(context: Context): Int {
        return ContextCompat.getColor(context, hb.xstyle.R.color.TH_Cyan800)
    }

}

/**
 * 样式5
 * 活跃人数: 128人
 */
class StyleOrange : FamilyTagStyle {
    override fun createTagDrawable(context: Context): Drawable {
        return ShapeBuilder()
            .corner(SizeUtils.dp2px(12f).toFloat())
            .solid(ContextCompat.getColor(context, hb.xstyle.R.color.TH_Orange100))
            .stroke(
                SizeUtils.dp2px(0.5f),
                ContextCompat.getColor(context, hb.xstyle.R.color.TH_Orange200)
            )
            .create()
    }

    override fun textColor(context: Context): Int {
        return ContextCompat.getColor(context, hb.xstyle.R.color.TH_Orange600)
    }

}

// endregion

// region 非特定标签样式

/**
 * 绿色样式 G800
 *
 */
class StyleGreen : FamilyTagStyle {
    override fun createTagDrawable(context: Context): Drawable {
        return ShapeBuilder()
            .corner(SizeUtils.dp2px(12f).toFloat())
            .solid(ContextCompat.getColor(context, hb.xstyle.R.color.TH_Green100))
            .stroke(
                SizeUtils.dp2px(0.5f),
                ContextCompat.getColor(context, hb.xstyle.R.color.TH_Green200)
            )
            .create()
    }

    override fun textColor(context: Context): Int {
        return ContextCompat.getColor(context, hb.xstyle.R.color.TH_Green800)
    }
}

/**
 * 蓝色样式 B800
 */
class StyleBlue800 : FamilyTagStyle {
    override fun createTagDrawable(context: Context): Drawable {
        return ShapeBuilder()
            .corner(SizeUtils.dp2px(12f).toFloat())
            .solid(ContextCompat.getColor(context, hb.xstyle.R.color.TH_Blue100))
            .stroke(
                SizeUtils.dp2px(0.5f),
                ContextCompat.getColor(context, hb.xstyle.R.color.TH_Blue200)
            )
            .create()
    }

    override fun textColor(context: Context): Int {
        return ContextCompat.getColor(context, hb.xstyle.R.color.TH_Blue800)
    }
}


// endregion
