package hb.monitor.familytag

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.google.android.flexbox.FlexDirection
import com.google.android.flexbox.FlexWrap
import com.google.android.flexbox.FlexboxItemDecoration
import com.google.android.flexbox.FlexboxLayoutManager
import hb.drawable.shape.shape.ShapeBuilder
import hb.monitor.familytag.provider.FamilyTagLoop1StyleProvider
import hb.monitor.jvmti.databinding.Demo2ActivityBinding
import hb.utils.SizeUtils
import hb.xadapter.XBaseAdapter
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch


/**
 * Demo2Activity
 *
 * <AUTHOR>
 * @date 2024-06-18
 */
class Demo2Activity : AppCompatActivity() {

    companion object {
        @JvmStatic
        fun start(context: Context) {
            val starter = Intent(context, Demo2Activity::class.java)
            context.startActivity(starter)
        }
    }

    private val mBinding by lazy {
        Demo2ActivityBinding.inflate(layoutInflater)
    }

    private val mViewModel by viewModels<FamilyTagViewModel>()

    private val mList1Adapter by lazy {
        XBaseAdapter(this)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(mBinding.root)
        initView()
        initView2()
        initData()
    }

    private fun initView2() {
//        mBinding.flexBox1.remove()
    }

    private fun initData() {
        mViewModel.fetchTagList()
        lifecycleScope.launch {
            mViewModel.tagListFlow.collectLatest {
                mList1Adapter.items = it
                mList1Adapter.notifyDataSetChanged()
            }
        }
    }

    private fun initView() {

        val layoutManager = object : FlexboxLayoutManager(this) {
            override fun canScrollVertically(): Boolean {
                return false
            }
        }
        val styleProvider = FamilyTagLoop1StyleProvider()
        layoutManager.flexDirection = FlexDirection.ROW
        layoutManager.flexWrap = FlexWrap.NOWRAP
        mBinding.tagList1.layoutManager = layoutManager
        mBinding.tagList1.addItemDecoration(FlexboxItemDecoration(this).apply {
            setDrawable(ShapeBuilder().size(SizeUtils.dp2px(20f), SizeUtils.dp2px(10f)).create())
        })
        mList1Adapter.setOnViewHolderCreatedListener {
            if (it is FamilyTagBaseViewHolder) {
                it.styleProvider = styleProvider
            }
        }
        mBinding.tagList1.adapter = mList1Adapter


        mList1Adapter.register(FamilyTagBean::class.java, FamilyTagViewHolder::class.java)
    }
}