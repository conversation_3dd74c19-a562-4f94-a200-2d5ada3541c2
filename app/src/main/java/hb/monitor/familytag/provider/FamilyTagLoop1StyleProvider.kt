package hb.monitor.familytag.provider

import hb.monitor.familytag.FamilyTagBean
import hb.monitor.familytag.tagstyle.FamilyTagStyle
import hb.monitor.familytag.tagstyle.StyleBlue800
import hb.monitor.familytag.tagstyle.StyleGreen
import hb.monitor.familytag.tagstyle.StyleOrange
import hb.monitor.familytag.tagstyle.StylePink
import hb.monitor.familytag.tagstyle.StylePurple

/**
 * 标签样式循环1
 *
 * <AUTHOR>
 * @date 2024-06-19
 */
class FamilyTagLoop1StyleProvider : FamilyTagStyleProvider {

    private var loopIndex = -1

    private val loopStyles =
        arrayOf(StyleGreen(), StyleBlue800(), StylePurple(), StylePink(), StyleOrange())

    /**
     * 扣除已展示的特殊标签，按顺序返回
     */
    override fun getStyle(familyTagBean: FamilyTagBean, position: Int): FamilyTagStyle {
        loopIndex++
        return loopStyles[loopIndex % loopStyles.size]
    }
}