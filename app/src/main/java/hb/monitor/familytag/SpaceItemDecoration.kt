package hb.monitor.familytag

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.google.android.flexbox.FlexboxLayoutManager


/**
 * 分割线 装饰器
 *
 * @param horizontalSpace 水平分割线
 * @param verticalSpace 垂直分割线
 * <AUTHOR>
 * @date 2024-06-19
 */
class SpaceItemDecoration(private val horizontalSpace: Int, private val verticalSpace: Int) :
    RecyclerView.ItemDecoration() {

    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        super.getItemOffsets(outRect, view, parent, state)
        val layoutManager = parent.layoutManager as? FlexboxLayoutManager ?: return
        val leftPosition = layoutManager.getDecoratedLeft(view)
        val topPosition = layoutManager.getDecoratedTop(view)
        if (leftPosition != 0) {
            outRect.left = horizontalSpace
        }
        if (topPosition != 0) {
            outRect.top = verticalSpace
        }
    }
}