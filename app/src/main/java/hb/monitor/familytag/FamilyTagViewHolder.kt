package hb.monitor.familytag

import android.view.ViewGroup
import android.widget.TextView
import hb.monitor.jvmti.R
import hb.monitor.jvmti.databinding.FamilyTagItemBinding

/**
 * 家族标签常用VH
 *
 * <AUTHOR>
 * @date 2024-06-19
 */
class FamilyTagViewHolder(viewGroup: ViewGroup) :
    FamilyTagBaseViewHolder(viewGroup, R.layout.family_tag_item) {
    private val mBinding by lazy {
        FamilyTagItemBinding.bind(itemView)
    }

    override fun getTagTextView(): TextView {
        return mBinding.tvTag
    }
}