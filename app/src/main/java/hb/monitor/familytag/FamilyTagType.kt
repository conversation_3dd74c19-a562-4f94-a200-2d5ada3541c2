package hb.monitor.familytag

/**
 * 家族标签类型
 *
 * <AUTHOR>
 * @date 2024-06-19
 */
sealed class FamilyTagType(
    val typeValue: String, // 标签类型值
//    val tagNormalStyle: // 标签基本样式
    val canEdit: <PERSON>olean = true, // 是否可编辑
) {
    /**
     * 特定标签
     */
    class SpecialTag() : FamilyTagType(typeValue = "1")

    /**
     * 兴趣标签
     */
    class InterestTag() : FamilyTagType(typeValue = "2")

    /**
     * 平台标签
     */
    class PlatformTag() : FamilyTagType(typeValue = "3")

    /**
     * 平台标签-未解锁
     */
    class PlatformUnlockTag() : FamilyTagType(typeValue = "4", canEdit = false)

    /**
     * 负面标签
     */
    class NegativeTag() : FamilyTagType(typeValue = "5", canEdit = false)


}