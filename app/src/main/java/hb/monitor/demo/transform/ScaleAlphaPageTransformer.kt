package hb.monitor.demo.transform

import android.util.Log
import android.view.View
import androidx.viewpager2.widget.ViewPager2
import hb.utils.Loger
import kotlin.math.abs

class ScaleAlphaPageTransformer( // 可以通过构造函数传入参数
    private val minScale: Float = 0.85f,
    private val minAlpha: Float = 0.7f
) : ViewPager2.PageTransformer {

    override fun transformPage(page: View, position: Float) {
//        Log.d("kyluzoi","transformPage: position = $position")
        page.apply {
            // pivotX 和 pivotY 确保缩放从中心开始（如果需要非中心缩放则调整）
            // ViewPager2 中的页面通常是match_parent，所以其中心就是其几何中心
            // 如果 Item 内部内容有特定对齐，这可能不需要或者需要基于内容调整
            // pivotX = width / 2f 
            // pivotY = height / 2f

            when {
                position < -1 || position > 1 -> { // 远处的页面 (完全在屏幕外或大部分在屏幕外)
                    alpha = minAlpha * 0.8f // 可以让更远的页面更透明一些
                    scaleX = minScale * 0.9f // 可以让更远的页面更小一些
                    scaleY = minScale * 0.9f
                    translationX = 0f // 不进行平移，MarginPageTransformer 会处理间距
                }
                else -> { // [-1, 1] 范围内的页面 (当前页、直接相邻页以及它们之间的过渡状态)
                    // 计算缩放比例: 越靠近中心 (position=0), scaleFactor 越接近 1
                    val scaleFactor = minScale.coerceAtLeast(1 - abs(position) * (1 - minScale))
                    scaleX = scaleFactor
                    scaleY = scaleFactor

                    // 计算透明度: 越靠近中心, alpha 越接近 1
                    alpha = minAlpha.coerceAtLeast(1 - abs(position) * (1 - minAlpha))
                    
                    translationX = 0f // 明确不进行平移
                }
            }
        }
    }
} 