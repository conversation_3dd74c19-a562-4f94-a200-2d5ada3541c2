package hb.monitor.demo

import android.content.Context
import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import hb.monitor.jvmti.R
import hb.monitor.jvmti.databinding.NodeViewBinding

/**
 *
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
class FamilyActiveBoxProgressAdapter(private val context: Context) :
    FamilyBoxProgressAdapter<String>(context) {
    private val mData = mutableListOf<String>()

    fun setData(data: List<String>) {
        mData.clear()
        mData.addAll(data)
        notifyDataSetChange()
    }

    override fun itemSize(): Int {
        return mData.size
    }

    override fun bindView(position: Int, parent: ViewGroup): View {
        val binding = NodeViewBinding.inflate(LayoutInflater.from(context), parent, true)
        binding.tvValue.text = mData[position]
        binding.anchorView.tag = R.id.progressAnchor
        return binding.root
    }

    override fun itemProgress(position: Int): Int {
        return mData[position].toInt()
    }

    override fun progressAnchorTag(): Any {
        return R.id.progressAnchor
    }

}