package hb.monitor.demo

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.widget.Button
import androidx.appcompat.app.AppCompatActivity
import hb.monitor.jvmti.R
import hb.monitor.weiget.HeartFlyView

/**
 * 爱心飘飞效果演示Activity
 */
class HeartFlyDemoActivity : AppCompatActivity() {

    private lateinit var heartFlyView: HeartFlyView
    private lateinit var btnEmitOne: Button
    private lateinit var btnEmitMultiple: Button
    private val handler = Handler(Looper.getMainLooper())

    companion object {
        private const val TAG = "HeartFlyDemoActivity"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_heart_fly_demo)
        
        // 初始化视图
        initViews()
        
        // 设置爱心飞行参数
        configureHeartFly()
        
        // 初始化点击事件
        initListeners()
        
        // 视图布局完成后延迟发射爱心
        // 直接使用handler处理，而不是在onCreate中立即调用
        handler.postDelayed({
            Log.d(TAG, "初始化完成，发射初始爱心")
            emitInitialHearts()
        }, 1000)
    }

    /**
     * 初始化视图
     */
    private fun initViews() {
        heartFlyView = findViewById(R.id.heart_fly_view)
        btnEmitOne = findViewById(R.id.btn_emit_one)
        btnEmitMultiple = findViewById(R.id.btn_emit_multiple)
    }
    
    /**
     * 配置爱心飞行效果参数
     */
    private fun configureHeartFly() {
        heartFlyView.setHeartResource(R.drawable.ic_star)

        // 设置加速度范围，让每个爱心速度不同
        heartFlyView.setAccelerationFactorRange(4f, 5.0f)
        
        // 设置初始速度范围
        heartFlyView.setInitialVelocityRange(0.2f, 0.5f)
        
        // 设置边界渐变阈值
        heartFlyView.setFadeOutThreshold(0.3f)
        
        // 设置顶部拐点比例 - 调整为更大的值，让爱心曲率更明显
        heartFlyView.setTopTurnPointRatio(0.1f)
        
        Log.d(TAG, "爱心飞行参数配置完成")
    }
    
    /**
     * 初始化点击事件
     */
    private fun initListeners() {
        // 发射单个爱心
        btnEmitOne.setOnClickListener {
            Log.d(TAG, "点击发射一个爱心")
            heartFlyView.emitHeart()
        }
        
        // 发射多个爱心
        btnEmitMultiple.setOnClickListener {
            Log.d(TAG, "点击发射多个爱心")
            emitMultipleHearts(20)
        }
    }
    
    /**
     * 发射初始爱心
     */
    private fun emitInitialHearts() {
        // 发射5个初始爱心
        emitMultipleHearts(5)
    }
    
    /**
     * 发射多个爱心
     */
    private fun emitMultipleHearts(count: Int) {
        try {
            // 逐个发射爱心而不是直接调用emitHearts
            for (i in 0 until count) {
                handler.postDelayed({
                    try {
                        heartFlyView.emitHeart()
                    } catch (e: Exception) {
                        Log.e(TAG, "发射爱心失败: ${e.message}")
                    }
                }, (i * 100).toLong())
            }
        } catch (e: Exception) {
            Log.e(TAG, "发射多个爱心失败: ${e.message}")
            e.printStackTrace()
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        // 移除所有回调，防止内存泄漏
        handler.removeCallbacksAndMessages(null)
    }
} 