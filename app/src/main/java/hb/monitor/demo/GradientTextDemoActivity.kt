package hb.monitor.demo

import android.graphics.LinearGradient
import android.graphics.Shader
import android.graphics.Typeface
import android.os.Bundle
import android.util.Log
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.content.ContextCompat
import androidx.core.view.setPadding
import hb.monitor.jvmti.R
import hb.monitor.weiget.GradientTextView
import hb.utils.ColorUtils
import hb.utils.SizeUtils

/**
 * GradientTextView演示Activity
 */
class GradientTextDemoActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.gradient_text_demo)
        
        // 可以通过代码动态修改控件属性
        val dynamicTextView = AppCompatTextView(this).apply {
            text = "x50"
            textSize = 40f
            setPadding(SizeUtils.dp2px(20f))
            // 设置斜体、加粗
            setTypeface(typeface, Typeface.BOLD_ITALIC)
            setBackgroundColor(ContextCompat.getColor(context,hb.xstyle.R.color.TH_Blue300))

            // 设置阴影
//            setShadowEffect(8f, 4f, 20f, android.graphics.Color.parseColor("#88000000"))

            // 设置布局参数
            layoutParams = android.widget.LinearLayout.LayoutParams(
                android.widget.LinearLayout.LayoutParams.WRAP_CONTENT,
                android.widget.LinearLayout.LayoutParams.WRAP_CONTENT
            )
            setTextColor(ColorUtils.getColor("88FFFFFF"))

            paint.shader = LinearGradient(
                0f,
                paddingTop.toFloat(),
                0f,
                textSize + paddingTop.toFloat(),
                intArrayOf(ColorUtils.getColor("#FF4336"),
                    ColorUtils.getColor("#FFC005")),
                null,
                Shader.TileMode.CLAMP
            )
        }

        
        // 将动态创建的控件添加到布局中
        findViewById<View>(android.R.id.content).rootView.findViewById<android.widget.LinearLayout>(
            R.id.gradient_text_container
        )?.addView(dynamicTextView)
    }
} 