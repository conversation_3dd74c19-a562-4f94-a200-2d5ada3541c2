package hb.monitor.demo.adapter // 假设放在 adapter 包下

import android.content.Context
import android.graphics.Color // 需要引入 Color
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat // 用于获取 Drawable
import androidx.core.graphics.drawable.DrawableCompat // 用于 Drawable Tint
import hb.monitor.weiget.IndicatorAdapter // 确保路径正确
import hb.monitor.demo.GalleryItemData // 确保路径正确
import hb.monitor.jvmti.R // 确保 R 文件路径正确
import kotlin.math.max
import kotlin.math.min

class GalleryIndicatorAdapterWithViews(
    private val context: Context,
    private val items: List<GalleryItemData>
) : IndicatorAdapter {

    private val inflater: LayoutInflater = LayoutInflater.from(context)

    private var cachedMaxHeightPx: Int? = null

    // 用于保存 ViewHolder，以便快速访问子视图
    private class ViewHolder(view: View) {
        val glowImageView: ImageView = view.findViewById(R.id.indicator_glow_image)
        val levelTextView: TextView = view.findViewById(R.id.indicator_level_text)
        val valueTextView: TextView = view.findViewById(R.id.indicator_value_text)
        // 可以添加根布局，用于整体缩放或透明度
        val rootView: View = view.findViewById(R.id.indicator_root_layout) 
    }

    override fun getCount(): Int = items.size

    private fun measureAndCacheDimensionsIfNeeded(view: View) {
        if (cachedMaxHeightPx == null) {

            val unspecifiedSpec = View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
            view.measure(unspecifiedSpec, unspecifiedSpec) // 测量以获取内容的自然尺寸
            cachedMaxHeightPx = view.measuredHeight
        }
    }

    override fun getPreferredIndicatorMaxHeightPx(): Int? {
        return cachedMaxHeightPx
    }

    override fun createAndBindData(position: Int, convertView: View?, parent: ViewGroup): View {
        val view: View
        val viewHolder: ViewHolder

        if (convertView == null) {
            // 注意：将parent参数传递为false，这样视图不会附加到parent上
            view = inflater.inflate(R.layout.layout_indicator_item, parent, false)
            viewHolder = ViewHolder(view)
            view.tag = viewHolder
        } else {
            view = convertView
            viewHolder = view.tag as ViewHolder
        }

        val item = items.getOrNull(position)
        if (item == null) {
            // 如果没有数据，可以隐藏视图或显示占位符
            view.visibility = View.INVISIBLE
            return view
        }
        view.visibility = View.VISIBLE

        // 1. 更新文本
        viewHolder.levelTextView.text = item.levelText
        viewHolder.valueTextView.text = item.valueText

        // 2. 更新整体透明度 (移除缩放，基于 selectionProgress)
        viewHolder.rootView.scaleX = 1.0f // 固定缩放为100%
        viewHolder.rootView.scaleY = 1.0f // 固定缩放为100%
        viewHolder.rootView.alpha = 0.5f // 初始半透明，让 updateViewScrollState 来激活
        viewHolder.glowImageView.alpha = 0f

        // 在首次创建视图时，确保我们有机会测量样本以填充缓存
        if (cachedMaxHeightPx == null) {
            measureAndCacheDimensionsIfNeeded(view)
        }

        return view
    }

    override fun updateViewScrollState(view: View, position: Int, selectionProgress: Float, globalScrollOffset: Float) {
        val viewHolder = view.tag as? ViewHolder ?: return // 确保 ViewHolder 存在
        
        val item = items.getOrNull(position)
        if (item == null) { // 如果对应项数据不存在
            view.visibility = View.INVISIBLE
            return
        }
        view.visibility = View.VISIBLE // 确保可见，以防万一

        // 更新透明度
        viewHolder.rootView.alpha = 0.5f + (selectionProgress * 0.5f) // 从 50% 透明到 100% 不透明
        
        val glowDrawable = ContextCompat.getDrawable(context, R.drawable.ic_glow_placeholder)?.mutate()
        glowDrawable?.let {
            viewHolder.glowImageView.setImageDrawable(it)
        }
        // 0.5~1.0 之间的渐变
        val glowAlpha = min(selectionProgress + 0.5f,1f) // 将范围从 [0.5, 1.0] 映射到 [0, 1]
        viewHolder.glowImageView.alpha = glowAlpha
    }

    // 可选的回调可以根据需要实现
    override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
        // Log.d("Adapter", "Scrolled: pos=$position, offset=$positionOffset")
    }

    override fun onPageSelected(position: Int) {
        // Log.d("Adapter", "Selected: $position")
    }

    override fun onPageScrollStateChanged(state: Int) {}
} 