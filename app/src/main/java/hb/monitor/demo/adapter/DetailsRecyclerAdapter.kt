package hb.monitor.demo.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import hb.monitor.jvmti.R

class DetailsRecyclerAdapter(private var items: List<String>) : 
    RecyclerView.Adapter<DetailsRecyclerAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_detail_recycler, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.detailItemText.text = items[position]
    }

    override fun getItemCount(): Int = items.size

    fun updateData(newItems: List<String>) {
        this.items = newItems
        notifyDataSetChanged() // Consider using DiffUtil for better performance
    }

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val detailItemText: TextView = view.findViewById(R.id.detailItemText)
    }
} 