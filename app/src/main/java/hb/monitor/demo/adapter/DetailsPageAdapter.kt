package hb.monitor.demo.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import hb.monitor.jvmti.databinding.ItemDetailsPageBinding

class DetailsPageAdapter(private val pageData: List<List<String>>) : RecyclerView.Adapter<DetailsPageAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemDetailsPageBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(pageData[position])
    }

    override fun getItemCount(): Int = pageData.size

    inner class ViewHolder(private val binding: ItemDetailsPageBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(contentItems: List<String>) {
            binding.detailRecyclerView.apply {
                layoutManager = LinearLayoutManager(context)
                adapter = DetailContentAdapter(contentItems)
            }
        }
    }
} 