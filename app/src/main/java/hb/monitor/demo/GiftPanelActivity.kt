package hb.monitor.demo

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import hb.monitor.jvmti.databinding.GiftBalanceTestBinding
import hb.utils.SizeUtils

/**
 *
 *
 * <AUTHOR>
 * @date 2024-11-12
 */
class GiftPanelActivity : AppCompatActivity() {

    companion object {
        @JvmStatic
        fun start(context: Context) {
            val starter = Intent(context, GiftPanelActivity::class.java)
            context.startActivity(starter)
        }
    }


    private val mBinding by lazy {
        GiftBalanceTestBinding.inflate(layoutInflater)
    }

    private var priceAvailableWidth = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(mBinding.root)
        initData()
    }

    private fun initData() {
//        lifecycleScope.launchWhenResumed {
//            mBinding.groupQuBalance.layoutParams =
//                (mBinding.groupQuBalance.layoutParams as ConstraintLayout.LayoutParams).apply {
//                    matchConstraintMaxWidth = (getAvailableSpaceWidth() * (160f / 227)).toInt()
//                }
//        }

        mBinding.btnVisible.setOnClickListener {
            mBinding.groupShellBalance.isVisible = !mBinding.groupShellBalance.isVisible
        }

        mBinding.btnInput1.setOnClickListener {
            val txt = mBinding.edInput.text

            mBinding.tvQuBalance.text = mBinding.edInput.text
        }

        mBinding.btnInput2.setOnClickListener {
            val txt = mBinding.edInput.text

            if (txt.length > 3) {
                mBinding.groupQuBalance.layoutParams =
                    (mBinding.groupQuBalance.layoutParams as ConstraintLayout.LayoutParams).apply {
                        matchConstraintMaxWidth = (getAvailableSpaceWidth() * (122f / 227)).toInt()
                            .coerceAtLeast((getAvailableSpaceWidth() * (160f / 227)).toInt() - (txt.length-3) * SizeUtils.dp2px(10f))
                    }
                mBinding.groupShellBalance.layoutParams =
                    (mBinding.groupShellBalance.layoutParams as ConstraintLayout.LayoutParams).apply {
                        matchConstraintMinWidth =
                            SizeUtils.dp2px(63f)
                    }
            } else {
                mBinding.groupQuBalance.layoutParams =
                    (mBinding.groupQuBalance.layoutParams as ConstraintLayout.LayoutParams).apply {
                        matchConstraintMaxWidth = (getAvailableSpaceWidth()).toInt()
                    }
                mBinding.groupShellBalance.layoutParams =
                    (mBinding.groupShellBalance.layoutParams as ConstraintLayout.LayoutParams).apply {
                        matchConstraintMinWidth =
                            SizeUtils.dp2px(63f) + (txt.length - 1) * SizeUtils.dp2px(10f)
                    }
            }

            mBinding.tvShellBalance.text = txt
        }
    }

    private fun getAvailableSpaceWidth(): Int {
        priceAvailableWidth =
            if (priceAvailableWidth > 0) priceAvailableWidth else mBinding.spaceAvailable.width
        return priceAvailableWidth
    }
}