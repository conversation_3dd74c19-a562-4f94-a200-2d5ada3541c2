package hb.monitor.demo

import android.content.Context
import android.content.Intent
import android.graphics.Typeface
import android.os.Bundle
import android.util.Log
import android.util.SparseArray
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentStatePagerAdapter
import androidx.lifecycle.lifecycleScope
import com.google.android.material.tabs.TabLayout
import hb.monitor.jvmti.R
import hb.monitor.jvmti.databinding.ActViewpagerTestBinding
import hb.monitor.jvmti.databinding.TestFragmentBinding
import hb.utils.Loger
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.random.Random

/**
 * viewpager 正确用法
 *
 * <AUTHOR>
 * @date 2025-01-06
 */
class ViewPagerTestActivity : AppCompatActivity() {

    companion object {
        @JvmStatic
        fun start(context: Context) {
            val starter = Intent(context, ViewPagerTestActivity::class.java)
            context.startActivity(starter)
        }
    }

    val randomTitle = listOf(
        "聊天",
        "好友",
        "消息",
        "首页",
        "年度活动",
        "半年活动",
        "111",
        "222",
        "3333",
        "444",
        "555"
    )


    private val mBinding by lazy {
        ActViewpagerTestBinding.inflate(layoutInflater)
    }

    val mTabList = mutableListOf<String>()

    val mErrorAdapter by lazy {
        Error1PageAdapter(supportFragmentManager, mTabList)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(mBinding.root)
        initView()
    }

    private fun initView() {
        mBinding.viewPager.adapter = mErrorAdapter
        mBinding.tabLayout.setupWithViewPager(mBinding.viewPager)
        mTabList.addAll(randomTitle)
        mErrorAdapter.notifyDataSetChanged()
        mBinding.btnDynamic.setOnClickListener {
            lifecycleScope.launch {
                mTabList.clear()
                val randomSize = Random(System.currentTimeMillis()).nextInt(5) + 1
                while (mTabList.size < randomSize) {
                    val title = randomTitle.random()
                    if (!mTabList.contains(title)) {
                        mTabList.add(title)
                    }
                }
                mErrorAdapter.notifyDataSetChanged()
                mBinding.viewPager.setCurrentItem(0, false)
            }
        }
    }


    override fun onDestroy() {
        super.onDestroy()
    }


    class Error1PageAdapter(fm: FragmentManager, private val tabList: List<String>) :
        FragmentStatePagerAdapter(
            fm,
            BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT
        ) {

        override fun getPageTitle(position: Int): CharSequence {
            return tabList[position]
        }

        override fun getCount(): Int {
            return tabList.size
        }

        override fun getItem(position: Int): Fragment {
            Log.d("kyluzoi", "getItem: $position -- > ${tabList[position]}")
            return Fragment1.newInstance(tabList[position], position).apply {
                arguments = (arguments ?: Bundle()).apply {
                    putString("title", tabList[position])
                }
            }
        }

        override fun instantiateItem(container: ViewGroup, position: Int): Any {
            return super.instantiateItem(container, position)
        }

        override fun destroyItem(container: ViewGroup, position: Int, fragment: Any) {
            super.destroyItem(container, position, fragment)
        }

        override fun getItemPosition(childObj: Any): Int {
            val preIndex = tabList.indexOf((childObj as Fragment).arguments?.getString("title"))
            Log.d(
                "kyluzoi",
                "getItemPosition -->$preIndex -- title:${
                    (childObj as Fragment).arguments?.getString("title")
                }"
            )
            return if (preIndex < 0) POSITION_NONE else preIndex
        }

    }
}


class Fragment1 : Fragment(R.layout.test_fragment) {

    companion object {
        fun newInstance(title: String, position: Int): Fragment1 {
            val args = Bundle()
            val fragment = Fragment1().apply {
                this.position = position
                this.title = title
            }
            return fragment
        }
    }

    var position: Int = -1
    var title: String = ""

    private val mBinding by lazy {
        TestFragmentBinding.bind(requireView())
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
//        Log.d(
//            "kyluzoi", "onCreate -->" + """position:$position
//            |title:$title
//            |obj:${this.hashCode()}
//        """.trimMargin()
//        )
        mBinding.tvTest.text = """position:$position
            |title:$title
            |obj:${this.hashCode()}
        """.trimMargin()
    }

}