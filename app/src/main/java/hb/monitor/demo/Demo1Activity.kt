package hb.monitor.demo

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import androidx.appcompat.app.AppCompatActivity
import hb.monitor.jvmti.databinding.Demo1ActivityBinding

/**
 *
 *
 * <AUTHOR>
 * @date 2024-02-05
 */
class Demo1Activity : AppCompatActivity() {
    companion object {
        @JvmStatic
        fun start(context: Context) {
            val starter = Intent(context, Demo1Activity::class.java)
            context.startActivity(starter)
        }
    }

    private val mBinding by lazy {
        Demo1ActivityBinding.inflate(layoutInflater)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(mBinding.root)
        val adapter = FamilyActiveBoxProgressAdapter(this)
        mBinding.draweeView.setImageFromUrl("https://forum01.jiaoliuqu.com/217404523740005146.gif_style.360")
        mBinding.familyProgress.setAdapter(adapter)
        mBinding.root.post {
            val datas = listOf(
                "10","20"
            )
            adapter.setData(
                datas
            )
            mBinding.familyProgress.setCurrentProgress(15)
        }
    }
}