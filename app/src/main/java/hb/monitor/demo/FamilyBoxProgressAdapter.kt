package hb.monitor.demo

import android.content.Context
import android.view.View
import android.view.ViewGroup

/**
 *
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
abstract class FamilyBoxProgressAdapter<Data>(context: Context) {

    private val mViews = mutableMapOf<Int, View>()

    private var mObserver: () -> Unit = {}


    fun registerViewObserver(observer: () -> Unit) {
        mObserver = observer
    }

    /**
     * 节点数
     */
    abstract fun itemSize(): Int

    /**
     * 节点视图
     */
    fun createItemView(position: Int, parent: ViewGroup): View {
        val view = bindView(position, parent)
        mViews[position] = view
        return view
    }

    /**
     * 绑定节点视图
     */
    abstract fun bindView(position: Int, parent: ViewGroup): View

    /**
     * 刷新页面
     */
    fun notifyDataSetChange() {
        mObserver()
    }

    /**
     * item 节点对应的进度值
     */
    abstract fun itemProgress(position: Int): Int

    /**
     * 进度条位置锚点的视图tag
     * 外部会通过 findViewByTag 找到锚点View
     * 进度条会在 view 的中心点绘制
     */
    abstract fun progressAnchorTag(): Any

    /**
     * 获取position 对应的视图
     */
    fun getPositionView(position: Int): View? {
        return mViews[position]
    }
}