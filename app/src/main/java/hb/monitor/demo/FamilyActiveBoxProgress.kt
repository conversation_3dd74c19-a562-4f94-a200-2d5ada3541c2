package hb.monitor.demo

import android.content.Context
import android.graphics.Color
import android.graphics.Paint
import android.graphics.drawable.GradientDrawable
import android.graphics.drawable.LayerDrawable
import android.graphics.drawable.PaintDrawable
import android.graphics.drawable.ScaleDrawable
import android.util.AttributeSet
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.HorizontalScrollView
import androidx.core.view.marginStart
import androidx.core.view.marginTop
import androidx.core.view.postDelayed
import hb.monitor.jvmti.R
import hb.monitor.jvmti.databinding.FamilyActiviteBoxProgressBinding
import kotlin.math.max
import kotlin.math.min

/**
 * 家族活跃宝箱进度条
 *
 * <AUTHOR>
 * @date 2024-02-05
 */
class FamilyActiveBoxProgress @JvmOverloads constructor(
    context: Context,
    attributeSet: AttributeSet? = null,
    defStyleAttr: Int = 0,
) : HorizontalScrollView(context, attributeSet, defStyleAttr) {

    private var mAdapter: FamilyBoxProgressAdapter<*>? = null

    /**
     * 进度条圆角
     */
    private var mRadius = 230f

    /**
     * 进度条填充色
     */
    private var mProgressSolidColor = Color.parseColor("#ffffff")

    /**
     * 渐变色开始颜色
     */
    private var mGradientStartColor = Color.parseColor("#FAFF00")

    /**
     * 渐变色中间颜色
     */
    private var mGradientMiddleColor = Color.parseColor("#FF0E9F")

    /**
     * 渐变色结束颜色
     */
    private var mGradientEndColor = Color.parseColor("#FA00FF")

    /**
     * 一屏最多显示节点个数
     */
    private var mMaxNodeCount = 5

    /**
     * 最小节点间距
     */
    private var mMiniMargin = 20

    private var mProgressValue = 0

    private val mBinding by lazy {
        FamilyActiviteBoxProgressBinding.inflate(LayoutInflater.from(context), this)
    }

    init {
        initAttr()
        initProgressbarUi()
        initProgressUiChange()
    }

    /**
     * 初始化属性
     */
    private fun initAttr() {

    }

    private fun initProgressUiChange() {
        // 监听 progress 的 parent 的尺寸变化
        (mBinding.progress.parent as ViewGroup).addOnLayoutChangeListener { view, left, _, right, _, _, _, _, _ ->
            val oldSize = mBinding.progress.width
            mAdapter?.let {
                val newSize = right - left
                if (oldSize != newSize) {
                    val anchorView =
                        view.findViewWithTag<View>(it.progressAnchorTag()) ?: return@let
                    val progressLayoutParams = mBinding.progress.layoutParams as? LayoutParams
                    progressLayoutParams?.let { params ->
                        params.width = newSize
                        params.topMargin =
                            (anchorView.top + anchorView.bottom) / 2 - mBinding.progress.height / 2
                        mBinding.progress.max = newSize
                        mBinding.progress.requestLayout()
                        setCurrentProgress(mProgressValue)
                    }

                }
            }

        }
    }

    private fun updateData() {
        mAdapter?.let {
            // 当前视图宽度
            val width = measuredWidth
            // 当前页能展示多少节点
            val nodeCount = max(min(mMaxNodeCount, it.itemSize()), 2)

            for (i in 0 until it.itemSize()) {
                // 先将节点添加到父布局中，等测量完成后再调整位置
                val nodeView = it.createItemView(i, mBinding.flProgressItems)
                post {
                    if (it.itemSize() == 2) {
                        // 只有两项item需要等分间距
                        buildTwoNodeMargin(nodeView, i, nodeCount, width)
                    } else {
                        val layoutParams = nodeView.layoutParams as? LayoutParams ?: LayoutParams(
                            ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT
                        )
                        // 单个节点视图真实宽度
                        val nodeRealWidth =
                            nodeView.width + layoutParams.marginStart + layoutParams.marginEnd
                        // 计算节点间距
                        val nodeMargin =
                            max(
                                mMiniMargin,
                                (width - (nodeCount * nodeRealWidth)) / (nodeCount - 1)
                            )

                        if (i != 0) {
                            // 第0个需要保持原有间距，不参与间距重置
                            layoutParams.marginStart =
                                (nodeMargin + nodeRealWidth) * i + layoutParams.marginStart
                            nodeView.layoutParams = layoutParams
                        }
                    }
                }
            }
        }
    }

    private fun buildTwoNodeMargin(nodeView: View, i: Int, nodeCount: Int, width: Int) {
        val layoutParams = nodeView.layoutParams as? LayoutParams ?: LayoutParams(
            ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT
        )
        // 单个节点视图真实宽度
        val nodeRealWidth =
            nodeView.width + layoutParams.marginStart + layoutParams.marginEnd
        // 计算节点间距
        val nodeMargin =
            max(mMiniMargin, (width - (nodeCount * nodeRealWidth)) / 4)

        // 第0个需要保持原有间距，不参与间距重置
        layoutParams.marginStart =
            (nodeMargin) * (i * 2 + 1) + nodeRealWidth * i + layoutParams.marginStart
        layoutParams.marginEnd = nodeMargin
        nodeView.layoutParams = layoutParams
    }

    /**
     * 进度条样式初始化
     */
    private fun initProgressbarUi() {
        //进度条背景填充色
        val shapeDrawableBg = PaintDrawable().apply {
            setCornerRadius(mRadius)
            paint.color = mProgressSolidColor
            paint.style = Paint.Style.FILL
        }


        //进度条渐变色
        val gradientDrawable = GradientDrawable().apply {
            cornerRadius = mRadius
            shape = GradientDrawable.RECTANGLE
            orientation = GradientDrawable.Orientation.LEFT_RIGHT
            colors =
                intArrayOf(mGradientStartColor, mGradientMiddleColor, mGradientEndColor) //添加颜色组
            gradientType = GradientDrawable.LINEAR_GRADIENT//设置线性渐变
        }

        val scaleDrawable = ScaleDrawable(
            gradientDrawable, Gravity.START, 1f, -1f
        )

        val layerDrawable = LayerDrawable(arrayOf(shapeDrawableBg, scaleDrawable)).apply {
            setId(0, android.R.id.background)
            setId(1, android.R.id.progress)
        }

        mBinding.progress.progressDrawable = layerDrawable
    }

    /**
     * 页面数据变更
     */
    private fun notifyDataChange() {
        updateData()
        mBinding.progress.max = 100
    }

    /**
     * 设置适配器
     */
    fun setAdapter(adapter: FamilyBoxProgressAdapter<*>) {
        mAdapter = adapter
        adapter.registerViewObserver(::notifyDataChange)
    }

    /**
     * 设置当前进度
     * 直接给真实的进度即可
     */
    fun setCurrentProgress(progress: Int) {
        mProgressValue = progress
        // 计算当前进度在总进度中的位置
        mAdapter?.let {
            // 之前的节点进度值
            var preProgress = 0
            // 当前进度在区间的偏移进度
            var currentProgressOffset = 1f
            // 最后已完成的位置
            var lastPosition = -1
            for (position in 0 until it.itemSize()) {
                val itemProgress = it.itemProgress(position)
                if (progress < itemProgress) {
                    // 200~300 当前 250 则 为 0.5
                    currentProgressOffset =
                        (progress - preProgress).toFloat() / (itemProgress - preProgress)
                    break
                }
                lastPosition = position
                preProgress = itemProgress
            }
            // max为视图宽度,当前进度为当前节点视图的位置 + 偏移进度
            // 最后完成的节点左侧位置
            var lastCompleteLeft = 0
            var lastUnCompleteLeft = Int.MAX_VALUE
            it.getPositionView(lastPosition)?.let { nodeView -> // 节点视图
                nodeView.findViewWithTag<View>(it.progressAnchorTag())?.let { anchorView -> // 锚点视图
                    lastCompleteLeft =
                        nodeView.marginStart + (anchorView.left + anchorView.right) / 2
                }
            }
            it.getPositionView(lastPosition + 1)?.let { nodeView ->
                nodeView.findViewWithTag<View>(it.progressAnchorTag())?.let { anchorView -> // 锚点视图
                    lastUnCompleteLeft =
                        nodeView.marginStart + (anchorView.left + anchorView.right) / 2
                }
            }

            val realProgress =
                lastCompleteLeft + (lastUnCompleteLeft - lastCompleteLeft) * currentProgressOffset
            mBinding.progress.progress = realProgress.toInt()
        }
    }

}