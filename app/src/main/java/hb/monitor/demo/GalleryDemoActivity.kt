package hb.monitor.demo

import android.content.Context
import android.content.Intent
import android.graphics.Color
import androidx.appcompat.app.AppCompatActivity
import android.os.Bundle
import android.widget.Toast
import androidx.core.graphics.ColorUtils
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.CompositePageTransformer
import androidx.viewpager2.widget.MarginPageTransformer
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.appbar.AppBarLayout
import hb.monitor.demo.adapter.DetailsRecyclerAdapter
import hb.monitor.demo.adapter.GalleryAdapter
import hb.monitor.demo.adapter.GalleryIndicatorAdapterWithViews
import hb.monitor.demo.transform.ScaleAlphaPageTransformer
import hb.monitor.jvmti.R
import hb.monitor.jvmti.databinding.ActivityGalleryDemoBinding
import kotlin.math.abs

class GalleryDemoActivity : AppCompatActivity() {

    private val binding by lazy {
        ActivityGalleryDemoBinding.inflate(layoutInflater)
    }
    private lateinit var detailsAdapter: DetailsRecyclerAdapter
    private var allDetailsData: List<List<String>> = emptyList()
    private val targetColor = Color.parseColor("#212645")

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)

        setupToolbar()
        setupGalleryViewPager()
        setupDetailsRecyclerView()
        setupViewPagerAndScrollLinkage()
        setupCurveIndicator()
    }

    private fun setupToolbar() {
        // Back button listener
        binding.backButton.setOnClickListener {
            onBackPressedDispatcher.onBackPressed() 
        }

        // Menu button listener
        binding.menuButton.setOnClickListener {
            // Handle menu click, e.g., show a popup menu
            Toast.makeText(this, "Menu clicked", Toast.LENGTH_SHORT).show()
        }
    }

    private fun setupGalleryViewPager() {
        val galleryItems = (1..5).map { "画廊 $it" }
        if (galleryItems.isEmpty()) {
            return
        }
        binding.galleryViewPager.adapter = GalleryAdapter(galleryItems)
        binding.galleryViewPager.offscreenPageLimit = 3
        val compositeTransformer = CompositePageTransformer()
        val marginPx = resources.getDimensionPixelOffset(R.dimen.viewpager_gallery_item_margin)
        compositeTransformer.addTransformer(MarginPageTransformer(marginPx))
        val scaleFactor = 100f / 150f
        compositeTransformer.addTransformer(ScaleAlphaPageTransformer(minScale = scaleFactor, minAlpha = 0.7f))
        binding.galleryViewPager.setPageTransformer(compositeTransformer)
    }

    private fun setupDetailsRecyclerView() {
        allDetailsData = (1..5).map { galleryIndex ->
            // 创建更多项目以便于测试滚动效果
            (1..30).map { itemIndex -> "画廊 ${galleryIndex} 的详情内容 $itemIndex" }
        }
        
        detailsAdapter = DetailsRecyclerAdapter(allDetailsData.getOrElse(0) { emptyList() })
        binding.detailsRecyclerView.layoutManager = LinearLayoutManager(this)
        binding.detailsRecyclerView.adapter = detailsAdapter
    }

    private fun setupViewPagerAndScrollLinkage() {
        binding.appBarLayout.addOnOffsetChangedListener(AppBarLayout.OnOffsetChangedListener { appBarLayout, verticalOffset ->
            val scrollProgress = -verticalOffset / appBarLayout.totalScrollRange.toFloat()
//            updateGalleryVisualEffects(scrollProgress)
            updateToolbarBackground(scrollProgress)
        })
        
        binding.galleryViewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                detailsAdapter.updateData(allDetailsData.getOrElse(position) { emptyList() })
            }
        })
    }
    
    private fun updateToolbarBackground(scrollProgress: Float) {
        val blendedColor = ColorUtils.blendARGB(Color.TRANSPARENT, targetColor, scrollProgress)
        binding.customHeaderLayout.setBackgroundColor(blendedColor)
//        binding.collapsingToolbarLayout.setContentScrimColor(blendedColor)
//        binding.collapsingToolbarLayout.setStatusBarScrimColor(blendedColor)
    }

    private fun updateGalleryVisualEffects(scrollProgress: Float) {
        val galleryAlpha = 1.0f - scrollProgress * 0.5f 
        binding.galleryViewPager.alpha = galleryAlpha
        
        val indicatorAlpha = 1.0f - scrollProgress * 0.9f 
        binding.curveIndicator.alpha = indicatorAlpha
        
        val scale = 1.0f - scrollProgress * 0.1f 
        binding.galleryViewPager.scaleX = scale
        binding.galleryViewPager.scaleY = scale
    }

    private fun setupCurveIndicator() {
        val indicatorItems = List(binding.galleryViewPager.adapter?.itemCount ?: 0) { index ->
            GalleryItemData(
                id = index,
                levelText = "Lv.${index + 1}",
                valueText = "${(index + 1) * 100 + index * 10}"
            )
        }
        val curveIndicator = binding.curveIndicator
        val indicatorAdapter = GalleryIndicatorAdapterWithViews(this, indicatorItems)
        curveIndicator.setAdapter(indicatorAdapter)
        curveIndicator.attachToViewPager(binding.galleryViewPager)
    }

    companion object {
        fun start(context: Context) {
            val intent = Intent(context, GalleryDemoActivity::class.java)
            context.startActivity(intent)
        }
    }
} 