package hb.monitor.demo

import android.os.Bundle
import android.view.MotionEvent
import android.view.View
import android.widget.Button
import android.widget.CheckBox
import android.widget.CompoundButton
import androidx.appcompat.app.AppCompatActivity
import hb.monitor.jvmti.R
import hb.monitor.weiget.HeartAnimationView
import android.os.Vibrator
import android.os.VibrationEffect
import android.content.Context
import android.os.Build

class HeartAnimationDemoActivity : AppCompatActivity() {
    
    private lateinit var heartView: HeartAnimationView
    private lateinit var heartButton: View
    private lateinit var collisionCheckBox: CheckBox
    private lateinit var vibrator: Vibrator
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_heart_animation_demo)
        
        heartView = findViewById(R.id.heart_animation_view)
        heartButton = findViewById(R.id.heart_button)
        collisionCheckBox = findViewById(R.id.collision_checkbox)
        
        // 获取震动服务
        vibrator = getSystemService(Context.VIBRATOR_SERVICE) as Vibrator
        
        // 设置碰撞开关
        collisionCheckBox.isChecked = heartView.enableBottomCollision
        collisionCheckBox.setOnCheckedChangeListener { _, isChecked ->
            heartView.enableBottomCollision = isChecked
        }
        
        // 为底部爱心按钮设置点击事件
        heartButton.setOnClickListener { 
            // 在按钮上方生成爱心
            val x = heartButton.x + heartButton.width / 2
            val y = heartButton.y
            heartView.addHearts(x, y, 8) // 每次点击添加8个爱心
            // 触发震动
            vibrateDevice()
        }
        
        // 为整个心形动画视图添加触摸事件
        heartView.setOnTouchListener { _, event ->
            if (event.action == MotionEvent.ACTION_DOWN) {
                // 在触摸位置生成爱心
                heartView.addHearts(event.x, event.y, 1) // 增加生成的爱心数量，更加明显
                // 触发震动
                vibrateDevice()
                true
            } else {
                false
            }
        }
    }
    
    // 震动设备的方法
    private fun vibrateDevice() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            // 新API使用VibrationEffect
            vibrator.vibrate(VibrationEffect.createOneShot(50, VibrationEffect.DEFAULT_AMPLITUDE))
        } else {
            // 旧版本的震动方法
            @Suppress("DEPRECATION")
            vibrator.vibrate(50)
        }
    }
} 