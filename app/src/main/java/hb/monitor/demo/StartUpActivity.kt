package hb.monitor.demo

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import hb.monitor.jvmti.R
import hb.monitor.uitls.bfs.ITaskCreator
import hb.monitor.uitls.bfs.Project
import hb.monitor.uitls.bfs.Task
import hb.monitor.uitls.bfs.TaskRuntime
import hb.monitor.uitls.bfs2.DirectedAcyclicGraph
import hb.monitor.uitls.bfs2.TaskScheduler
import kotlinx.coroutines.delay

/**
 *
 *
 * <AUTHOR>
 * @date 2024-07-26
 */
class StartUpActivity : ComponentActivity() {

    companion object {
        @JvmStatic
        fun start(context: Context) {
            val starter = Intent(context, StartUpActivity::class.java)
            context.startActivity(starter)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Project.Builder(
            "activity 启动器测试",
            TaskRuntime(this),
            object : ITaskCreator {
                override fun createTask(taskName: String, taskRuntime: TaskRuntime): Task {
                    return createTask(
                        taskName,
                        taskRuntime,
                        if(taskName == "同步任务2") true else false
                    )
                }
            }
        )
//            .addBlockTask("阻塞任务1")
//            .addBlockTask("阻塞任务2").dependOn("阻塞任务4")
//            .addBlockTask("阻塞任务3")
//            .addBlockTask("阻塞任务4")
//            .addBlockTask("阻塞任务5")
//            .addBlockTask("阻塞任务6")
//            .addBlockTask("阻塞任务7")
            .addTask("异步任务1")
            .addTask("同步任务2").dependOn("异步任务1")
            .addTask("异步任务3").dependOn("同步任务2")
            .addTask("异步任务4").dependOn("异步任务3")
            .addTask("异步任务5").dependOn("异步任务4")
            .addTask("异步任务6").dependOn("异步任务5")
            .addTask("异步任务7").dependOn("异步任务6")
            .build().startProject()

        setContentView(R.layout.test_activity)

    }

    fun createTask(taskName: String, taskRuntime: TaskRuntime, isAsync: Boolean): Task {
        return object :
            Task(taskName, taskRuntime = taskRuntime, isAsyncTask = isAsync, delayMills = 200) {
            override suspend fun run(name: String) {
                delay(1000)
                Log.e("kyluzoi", "task $taskName, $isAsync,finished")
            }
        }
    }
}