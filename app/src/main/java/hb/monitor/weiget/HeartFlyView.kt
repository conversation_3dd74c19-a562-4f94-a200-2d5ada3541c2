package hb.monitor.weiget

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.PointF
import android.graphics.Rect
import android.util.AttributeSet
import android.util.Log
import android.view.View
import android.view.animation.Interpolator
import androidx.core.content.ContextCompat
import androidx.core.graphics.drawable.DrawableCompat
import androidx.core.graphics.drawable.toBitmap
import hb.monitor.jvmti.R
import hb.utils.SizeUtils
import java.util.ArrayList
import java.util.Random
import kotlin.math.sin

/**
 * 爱心飘飞特效视图
 * 从底部发射爱心，像气球飞向上方，轨迹随机
 */
open class HeartFlyView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    // 爱心图像资源ID
    private var heartResId = -1

    // 爱心图像
    private var heartBitmap: Bitmap? = null

    // 画笔
    protected val paint = Paint().apply {
        isAntiAlias = true
    }

    // 随机数生成器
    private val random = Random()

    // 正在显示的爱心对象列表
    private val hearts = ArrayList<Heart>()

    // 对象池，用于回收Heart对象
    private val heartPool = ArrayList<Heart>()

    // 正在运行的动画器列表，用于清理
    private val runningAnimators = ArrayList<ValueAnimator>()

    // 待发射的爱心数量
    private var pendingHeartCount = 0

    // 所有爱心动画完成的回调
    private var allAnimationsCompletedListener: OnAllAnimationsCompletedListener? = null

    // 爱心尺寸范围
    private val minHeartSize = SizeUtils.dp2px(24f)
    private val maxHeartSize = SizeUtils.dp2px(36f)

    // 动画时长范围 (ms)
    private val minDuration = 2000
    private val maxDuration = 4000

    // 最大同时显示的爱心数量
    private val maxHeartCount = 30

    // 加速度系数范围 - 控制每个爱心的加速度
    private var minAccelerationFactor = 2.0f
    private var maxAccelerationFactor = 4.0f

    // 加速度系数 - 控制加速度大小，可调整
    private var accelerationFactor = 3.0f

    // 初始速度范围 - 控制每个爱心的初始速度
    // 值的范围在0.0~1.0之间，0表示静止，1表示最大初速度
    private var minInitialVelocity = 0.1f
    private var maxInitialVelocity = 0.4f

    // 边界透明度渐变阈值 (0.0-1.0)
    // 当心形接近边界这个比例时开始渐变
    private var fadeOutThreshold = 0.15f

    // 顶部拐点比例 (0.0-1.0)
    // 值越小，转折点越接近顶部
    // 0表示在屏幕最顶部转向，1表示在屏幕最底部转向
    private var topTurnPointRatio = 0.2f

    /**
     * 所有动画完成的回调接口
     */
    interface OnAllAnimationsCompletedListener {
        /**
         * 当所有爱心动画完成时调用
         */
        fun onAllAnimationsCompleted()
    }

    companion object {
        private const val TAG = "HeartFlyView"
    }

    init {
        Log.d(TAG, "初始化HeartFlyView")
    }

    /**
     * 加载爱心图像资源
     */
    private fun loadHeartBitmap() {
        try {
            Log.d(TAG, "开始加载爱心图像: 资源ID=$heartResId")
            val drawable = ContextCompat.getDrawable(context, heartResId)?.mutate()
            if (drawable == null) {
                Log.e(TAG, "加载爱心图像失败: drawable为null")
                return
            }
            val tintedDrawable = DrawableCompat.wrap(drawable)
            heartBitmap = tintedDrawable.toBitmap()
        } catch (e: Exception) {
            Log.e(TAG, "加载爱心图像过程中发生异常: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 设置爱心资源ID
     */
    fun setHeartResource(resId: Int) {
        heartResId = resId
        loadHeartBitmap()
    }

    /**
     * 设置加速度系数范围
     * @param minFactor 最小加速度系数
     * @param maxFactor 最大加速度系数
     */
    fun setAccelerationFactorRange(minFactor: Float, maxFactor: Float) {
        if (minFactor > 0 && maxFactor >= minFactor) {
            this.minAccelerationFactor = minFactor
            this.maxAccelerationFactor = maxFactor
            // 设置平均值为默认加速度
            this.accelerationFactor = (minFactor + maxFactor) / 2f
            Log.d(TAG, "设置加速度系数范围: $minFactor ~ $maxFactor")
        }
    }

    /**
     * 设置边界透明度渐变阈值
     * @param threshold 阈值 (0.0-1.0)
     */
    fun setFadeOutThreshold(threshold: Float) {
        if (threshold in 0.0f..1.0f) {
            this.fadeOutThreshold = threshold
            Log.d(TAG, "设置边界透明度渐变阈值: $threshold")
        }
    }

    /**
     * 设置顶部拐点比例
     * @param ratio 比例 (0.0-1.0)
     */
    fun setTopTurnPointRatio(ratio: Float) {
        if (ratio in 0.0f..1.0f) {
            this.topTurnPointRatio = ratio
            Log.d(TAG, "设置顶部拐点比例: $ratio")
        }
    }

    /**
     * 设置初始速度范围
     * @param minVelocity 最小初始速度 (0.0-1.0)
     * @param maxVelocity 最大初始速度 (0.0-1.0)
     */
    fun setInitialVelocityRange(minVelocity: Float, maxVelocity: Float) {
        if (minVelocity >= 0f && maxVelocity <= 1f && minVelocity <= maxVelocity) {
            this.minInitialVelocity = minVelocity
            this.maxInitialVelocity = maxVelocity
            Log.d(TAG, "设置初始速度范围: $minVelocity ~ $maxVelocity")
        }
    }

    /**
     * 发射一个爱心
     */
    open fun emitHeart() {
        // 检查图像资源是否已加载
        if (heartBitmap == null) {
            loadHeartBitmap()
            if (heartBitmap == null) {
                Log.e(TAG, "心形图片加载失败，无法发射爱心")
                return
            }
        }

        Log.d(TAG, "开始发射爱心")

        // 创建一个爱心
        // minHeartSize ~ maxHeartSize 随机
        val heartSize = random.nextInt(maxHeartSize - minHeartSize) + minHeartSize
        val heart = obtainHeart()

        // 设置爱心的初始位置（底部中心）
        heart.reset(width / 2f, height - 100f, heartSize)
        Log.d(TAG, "爱心初始化: 位置(${heart.x}, ${heart.y}), 大小=${heart.size}")

        // 创建向上飘的动画
        val animator = ValueAnimator.ofFloat(0f, 1f)

        // 添加到正在运行的动画列表中
        runningAnimators.add(animator)

        // 设置动画时长 (缩短时间范围，提高速度)
        val duration = random.nextInt(1000) + 3000L
        animator.duration = duration

        // 为每个爱心随机设置加速度系数，使每个爱心速度不同
        val heartAcceleration = minAccelerationFactor + random.nextFloat() * (maxAccelerationFactor - minAccelerationFactor)

        // 为每个爱心随机设置初始速度
        val heartInitialVelocity = minInitialVelocity + random.nextFloat() * (maxInitialVelocity - minInitialVelocity)

        // 使用自定义插值器，结合初始速度和加速度
        animator.interpolator = HeartInterpolator(heartInitialVelocity, heartAcceleration)

        // 创建贝塞尔曲线控制点
        createBezierPoints(heart)

        animator.addUpdateListener { animation ->
            // 更新位置
            val value = animation.animatedValue as Float

            // 更新爱心位置和透明度
            updateHeartProperties(heart, value, animator)

            invalidate()
        }

        animator.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator?) {
                super.onAnimationEnd(animation)
                // 动画结束后，移除爱心并回收
                hearts.remove(heart)
                heartPool.add(heart)

                // 从运行列表中移除
                runningAnimators.remove(animator)

                // 检查是否所有动画都完成了
                checkAllAnimationsCompleted()

                invalidate()
            }
        })

        // 将爱心添加到列表中
        hearts.add(heart)

        // 开始动画
        animator.start()
    }

    /**
     * 连续发射多个爱心
     */
    open fun emitHearts(count: Int) {
        // 记录待发射的爱心数量
        pendingHeartCount = count

        for (i in 0 until count) {
            postDelayed({
                // 发射爱心
                emitHeart()

                // 减少待发射计数
                pendingHeartCount--

                // 检查是否完成所有动画
                if (pendingHeartCount == 0) {
                    checkAllAnimationsCompleted()
                }
            }, (100 * i).toLong())
        }
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        if (hearts.isEmpty()) {
            return
        }

        try {
            // 绘制所有爱心
            for (i in 0 until hearts.size) {
                drawHeart(canvas, hearts[i])
            }
        } catch (e: Exception) {
            Log.e(TAG, "onDraw过程中发生异常: ${e.message}")
            e.printStackTrace()
        }
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
    }

    /**
     * 绘制单个爱心
     */
    private fun drawHeart(canvas: Canvas, heart: Heart) {
        if (heartBitmap == null) {
            return
        }

        heartBitmap?.let { bitmap ->
            try {
                // 保存画布
                canvas.save()
                // 计算当前位置（根据贝塞尔曲线和进度计算）
                val point = calculateBezierPoint(heart.progress, heart.bezierPoints)

                // 位移到当前位置
                canvas.translate(point.x - heart.size / 2, point.y - heart.size / 2)

                // 设置透明度，始终保持不透明
                paint.alpha = heart.alpha

                // 移除旋转效果，仅保留缩放
                val scale = 1.0f  // 保持固定大小

                // 仅缩放
                canvas.scale(scale, scale, heart.size / 2f, heart.size / 2f)

                // 检查矩形是否有效
                if (heart.rect.width() <= 0 || heart.rect.height() <= 0) {
                    heart.rect.set(0, 0, heart.size, heart.size)
                }
                // 绘制爱心图像
                canvas.drawBitmap(bitmap, null, heart.rect, paint)
                // 恢复画布
                canvas.restore()
            } catch (e: Exception) {
                Log.e(TAG, "绘制爱心过程中发生异常: ${e.message}")
                e.printStackTrace()
            }
        }
    }

    /**
     * 计算贝塞尔曲线上的点
     */
    private fun calculateBezierPoint(t: Float, points: Array<PointF>): PointF {
        val u = 1 - t
        val tt = t * t
        val uu = u * u
        val uuu = uu * u
        val ttt = tt * t

        val p = PointF()

        p.x = uuu * points[0].x + 3 * uu * t * points[1].x + 3 * u * tt * points[2].x + ttt * points[3].x
        p.y = uuu * points[0].y + 3 * uu * t * points[1].y + 3 * u * tt * points[2].y + ttt * points[3].y

        return p
    }

    /**
     * 从对象池获取Heart对象，如果池中没有则创建新的
     */
    private fun obtainHeart(): Heart {
        // 尝试从对象池中获取
        val poolList = heartPool
        if (poolList.isNotEmpty()) {
            val heart = poolList.removeAt(poolList.size - 1)
            return heart
        }

        // 没有可用的对象，创建新的
        val heart = Heart()
        Log.d(TAG, "创建新的爱心对象:")
        return heart
    }

    /**
     * 爱心对象类
     */
    inner class Heart(var size: Int = minHeartSize) {
        // 位置
        var x = 0f
        var y = 0f
        // 动画进度
        var progress = 0f
        // 透明度
        var alpha = 255
        // 贝塞尔曲线控制点
        val bezierPoints = Array(4) { PointF() }
        // 绘制区域
        val rect = Rect(0, 0, size, size)
        // 摇摆方向和强度
        var swayDirection = if (random.nextBoolean()) 1 else -1
        var swayFactor = 0.4f + random.nextFloat() * 1.4f
        // 固定1次摇摆
        var swayCount = 1

        init {

        }

        fun reset(x: Float, y: Float, size: Int) {
            this.x = x
            this.y = y
            this.size = size
            this.progress = 0f
            this.alpha = 255

            // 设置绘制区域
            rect.left = 0
            rect.top = 0
            rect.right = size
            rect.bottom = size

            // 重置摇摆参数
            swayDirection = if (random.nextBoolean()) 1 else -1
            swayFactor = 0.4f + random.nextFloat() * 1.4f
            // 固定1次摇摆
            swayCount = 1
        }
    }

    /**
     * 创建贝塞尔曲线控制点
     */
    private fun createBezierPoints(heart: Heart) {
        val viewHeight = height
        val viewWidth = width

        // 起点（底部中心）
        heart.bezierPoints[0] = PointF(heart.x, heart.y)

        // 随机决定飞行方向：0=左侧飞出，1=顶部飞出，2=右侧飞出
        val flyDirection = random.nextInt(3)

        // 计算转折点（开始转弯的位置）
        // topTurnPointRatio=0表示在最顶部，=1表示在底部
        val turnY = topTurnPointRatio * viewHeight

        // 根据飞行方向确定终点
        val endX: Float
        val endY: Float

        when (flyDirection) {
            0 -> {
                // 向左飞出
                endX = -heart.size * 2f  // 左侧屏幕外
                endY = turnY * 0.8f      // 略高于转折点
            }
            1 -> {
                // 向顶部飞出
                endX = heart.x + random.nextInt(viewWidth / 2) - viewWidth / 4f  // 顶部随机位置
                endY = -heart.size * 2f  // 顶部屏幕外
            }
            else -> {
                // 向右飞出
                endX = viewWidth + heart.size * 2f  // 右侧屏幕外
                endY = turnY * 0.8f      // 略高于转折点
            }
        }

        heart.bezierPoints[3] = PointF(endX, endY)

        // 控制点1 - 设置基础路径，摇摆效果会在动画中动态添加
        heart.bezierPoints[1] = PointF(
            // 保持与起点X相近，只有很小的随机偏移
            heart.x + (random.nextFloat() - 0.5f) * viewWidth * 0.05f,
            // 在起点和转折点之间
            heart.y * 0.4f + turnY * 0.6f
        )

        // 控制点2 - 控制转向
        val controlX = when (flyDirection) {
            0 -> heart.x + (endX - heart.x) * 0.3f  // 向左
            1 -> endX  // 向上
            else -> heart.x + (endX - heart.x) * 0.3f  // 向右
        }

        val controlY = when (flyDirection) {
            0, 2 -> turnY  // 左右方向，控制点Y在转折点
            else -> turnY * 0.3f  // 上方向，控制点Y在转折点上方
        }

        heart.bezierPoints[2] = PointF(controlX, controlY)
    }

    /**
     * 更新爱心位置和透明度
     */
    private fun updateHeartProperties(heart: Heart, progress: Float, animator: ValueAnimator? = null) {
        // 更新进度
        heart.progress = progress

        // 应用摇摆效果
        applySwayEffect(heart, progress)

        // 获取当前位置
        val currentPoint = calculateBezierPoint(progress, heart.bezierPoints)

        // 检测是否飘出屏幕边界
        val isOutOfScreen = isPointOutOfScreen(currentPoint)

        // 如果已经飘出屏幕边界，立即结束动画并回收
        if (isOutOfScreen && animator != null) {
            animator.cancel()  // 取消动画，这会触发onAnimationEnd回调
            return
        }

        // 计算到边界的距离比例
        val distanceToRightBorder = width - currentPoint.x
        val distanceToLeftBorder = currentPoint.x
        val distanceToTopBorder = currentPoint.y
        val distanceToBottomBorder = height - currentPoint.y

        // 找出最小的边界距离
        val minBorderDistance = minOf(
            distanceToRightBorder,
            distanceToLeftBorder,
            distanceToTopBorder,
            distanceToBottomBorder
        )

        // 计算最小的边界距离相对于视图尺寸的比例
        val viewSize = minOf(width, height).toFloat()
        val borderDistanceRatio = minBorderDistance / viewSize

        // 当接近边界时应用透明度渐变
        if (borderDistanceRatio < fadeOutThreshold) {
            // 计算透明度（从1变为0）
            val alphaRatio = borderDistanceRatio / fadeOutThreshold
            heart.alpha = (255 * alphaRatio).toInt().coerceIn(0, 255)
        } else {
            // 不接近边界时保持完全不透明
            heart.alpha = 255
        }
    }

    /**
     * 判断点是否超出屏幕边界
     */
    private fun isPointOutOfScreen(point: PointF): Boolean {
        // 增加边界外延，确保完全飘出后才判定为出界
        val margin = maxHeartSize

        return point.x < -margin ||             // 左边界
               point.x > width + margin ||      // 右边界
               point.y < -margin ||             // 上边界
               point.y > height + margin        // 下边界
    }

    /**
     * 应用摇摆效果，让爱心在上升过程中有限次摇摆
     */
    private fun applySwayEffect(heart: Heart, progress: Float) {
        // 计算完整摇摆周期（π是半个周期，所以一个完整摇摆是2π）
        // 根据摇摆次数调整频率，确保在整个动画过程中只摇摆指定次数
        val swayFrequency = heart.swayCount * Math.PI * 2

        // 只在动画初期应用摇摆
        if (progress < 0.3f) {
            // 创造有限次数的摇摆：将进度缩放到[0, swayFrequency]的范围内
            val scaledProgress = progress * (1/0.3f) * swayFrequency.toFloat() // 在0.3范围内完成摇摆

            // 计算当前摇摆量：使用正弦函数产生摇摆效果
            val swayAmount = sin(scaledProgress.toDouble()).toFloat() * heart.swayDirection * heart.swayFactor

            // 不减弱摇摆强度，保持原始摇摆强度，增加宽度因子使摇摆更明显
            val actualSwayAmount = swayAmount * width * 0.08f

            // 保存原始的贝塞尔曲线控制点Y值
            val originalY1 = heart.bezierPoints[1].y
            val originalY2 = heart.bezierPoints[2].y

            // 动态修改控制点，产生摇摆效果
            val point1 = heart.bezierPoints[1]
            val point2 = heart.bezierPoints[2]

            // 只修改X坐标，保持Y坐标不变，这样摇摆只影响水平速度
            point1.x = heart.bezierPoints[0].x + actualSwayAmount
            point2.x = point2.x + actualSwayAmount * 0.5f

            // 确保Y坐标不变，保持原始Y轴速度
            point1.y = originalY1
            point2.y = originalY2
        }
    }

    /**
     * 自定义插值器，支持初始速度和加速度
     * 组合了初始速度和加速度效果
     */
    private inner class HeartInterpolator(
        private val initialVelocity: Float, // 初始速度 (0.0-1.0)
        private val acceleration: Float     // 加速度系数
    ) : Interpolator {

        override fun getInterpolation(input: Float): Float {
            // 基础加速插值
            val acceleratedValue = input * input * acceleration

            // 线性速度分量（初始速度）
            val initialValue = initialVelocity * input

            // 组合效果：初始速度 + 加速效果
            // 随着时间推移，加速效果会逐渐占主导
            var result = initialValue + acceleratedValue * (1 - initialVelocity)

            return result
        }
    }

    /**
     * 设置所有动画完成的回调
     * @param listener 回调接口实例
     */
    fun setOnAllAnimationsCompletedListener(listener: OnAllAnimationsCompletedListener?) {
        this.allAnimationsCompletedListener = listener
    }

    /**
     * 清理所有动画，防止内存泄漏
     */
    fun clearAnimations() {
        // 停止所有运行中的动画
        for (animator in runningAnimators) {
            animator.removeAllListeners()
            animator.removeAllUpdateListeners()
            animator.cancel()
        }

        // 清空动画列表
        runningAnimators.clear()

        // 清空爱心列表
        hearts.clear()

        // 重置待发射计数
        pendingHeartCount = 0

        // 刷新视图
        invalidate()
    }

    /**
     * 视图从窗口分离时调用，清理资源
     */
    override fun onDetachedFromWindow() {
        // 在视图分离时清理所有动画
        clearAnimations()

        // 调用父类方法
        super.onDetachedFromWindow()
    }

    /**
     * 检查是否所有动画都已完成
     */
    private fun checkAllAnimationsCompleted() {
        // 检查是否没有正在运行的动画和待发射的爱心
        if (runningAnimators.isEmpty() && pendingHeartCount == 0) {
            Log.d(TAG, "所有动画已完成，触发回调")

            // 通知监听器所有动画已完成
            allAnimationsCompletedListener?.onAllAnimationsCompleted()
        }
    }
} 