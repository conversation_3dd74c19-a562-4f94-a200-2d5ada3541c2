package hb.monitor.weiget

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.LinearGradient
import android.graphics.Paint
import android.graphics.Shader
import android.util.AttributeSet
import android.util.Log
import androidx.appcompat.widget.AppCompatTextView
import hb.monitor.jvmti.R

/**
 * 自定义渐变文本控件，支持文字渐变、描边和阴影效果
 */
class GradientTextView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AppCompatTextView(context, attrs, defStyleAttr) {

    // --- 可配置属性 ---
    private var gradientColors: IntArray = intArrayOf()
    private var strokeColor: Int = Color.WHITE
    private var strokeWidth: Float = 1f

    // --- 多层阴影 --- 
    data class ShadowLayer(
        val radius: Float,
        val dx: Float,
        val dy: Float,
        val color: Int
    )

    private var shadowLayers = arrayOf(
        ShadowLayer(4f, 2f, 2f, Color.parseColor("#A0000000")),
        ShadowLayer(12f, 6f, 6f, Color.parseColor("#60000000")),
        ShadowLayer(24f, 12f, 12f, Color.parseColor("#30000000"))
    )

    // --- 内部状态 ---
    private var textShader: Shader? = null // 缓存渐变Shader
    private val maxShadowDy: Float
        get() = shadowLayers.maxOfOrNull { it.dy } ?: 0f
    private var textX: Float = 0f
    private var textY: Float = 0f

    init {
        attrs?.let { readAttrs(it) }
    }

    private fun readAttrs(attrs: AttributeSet) {
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.GradientTextView)
        try {
            val gradientStartColor =
                typedArray.getColor(R.styleable.GradientTextView_gradientStartColor, -1)
            val gradientEndColor =
                typedArray.getColor(R.styleable.GradientTextView_gradientEndColor, -1)
            if (gradientStartColor != -1 && gradientEndColor != -1) {
                gradientColors = intArrayOf(gradientStartColor, gradientEndColor)
            }
            strokeColor = typedArray.getColor(R.styleable.GradientTextView_strokeColor, strokeColor)
            strokeWidth =
                typedArray.getDimension(R.styleable.GradientTextView_strokeWidth, strokeWidth)
            // 注意：多层阴影目前不支持XML配置，如有需要可扩展
        } finally {
            typedArray.recycle()
        }
    }

    private fun updateGradientShader() {
        if (width == 0 || height == 0 || gradientColors.isEmpty()) { // 仅在使用渐变且有尺寸时创建
            textShader = null
            return
        }
        textShader = LinearGradient(
            0f,
            paddingTop.toFloat(),
            0f,
            height - paddingBottom.toFloat() - maxShadowDy,
            gradientColors,
            null,
            Shader.TileMode.CLAMP
        )
    }

    // --- 公开API --- 

    fun setGradientColors(startColor: Int, endColor: Int) {
        this.gradientColors = intArrayOf(startColor, endColor)
        updateGradientShader()
        invalidate()
    }

    fun setStroke(color: Int, width: Float) {
        this.strokeColor = color
        this.strokeWidth = width
        invalidate()
    }

    /**
     * 设置多层阴影效果
     * @param layers ShadowLayer 数组
     */
    fun setShadowLayers(layers: Array<ShadowLayer>) {
        this.shadowLayers = layers
        updateGradientShader() // 阴影变化影响渐变范围
        invalidate()
    }

    // --- 绘制逻辑 ---

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        // 计算文本绘制位置
        updateTextPosition()
        // 尺寸变化时更新渐变Shader
        updateGradientShader()
    }

    override fun setPadding(left: Int, top: Int, right: Int, bottom: Int) {
        super.setPadding(left, top, right, bottom)
        updateTextPosition()
    }

    override fun setPaddingRelative(start: Int, top: Int, end: Int, bottom: Int) {
        super.setPaddingRelative(start, top, end, bottom)
        updateTextPosition()
    }

    private fun updateTextPosition() {
        if (width == 0 || height == 0) return
        val availableWidth = width - paddingLeft - paddingRight
        val availableHeight = height - paddingTop - paddingBottom
        val textWidth = paint.measureText(text.toString())
        val fm = paint.fontMetrics

        textX = paddingLeft + (availableWidth - textWidth) / 2
        textY = paddingTop + (availableHeight - (fm.bottom - fm.top)) / 2 - fm.top
    }

    override fun onDraw(canvas: Canvas) {
        if (text.isNullOrEmpty() || width == 0 || height == 0) return

        val textPaint = paint
        val originalColor = textPaint.color
        val originalShader = textPaint.shader
        val originalStyle = textPaint.style
        val originalStrokeWidth = textPaint.strokeWidth
        val textToDraw = text.toString()

        setLayerType(LAYER_TYPE_SOFTWARE, null)

        // 1. 绘制多层阴影
        if (shadowLayers.isNotEmpty()) {
            textPaint.style = Paint.Style.FILL
            for (layer in shadowLayers) {
                if (layer.radius > 0 && Color.alpha(layer.color) > 0) { // 只有有效阴影才绘制
                    textPaint.setShadowLayer(layer.radius, layer.dx, layer.dy, layer.color)
                    canvas.drawText(textToDraw, textX, textY, textPaint)
                }
            }
            textPaint.clearShadowLayer()
        }

        // 2. 绘制描边
        if (strokeWidth > 0) {
            textPaint.shader = null // 描边不用渐变
            textPaint.style = Paint.Style.STROKE
            textPaint.strokeWidth = strokeWidth
            textPaint.color = strokeColor
            canvas.drawText(textToDraw, textX, textY, textPaint)
        }

        // 3. 绘制文本本体
        textPaint.style = Paint.Style.FILL
        textPaint.strokeWidth = originalStrokeWidth

        if (textShader != null) {
            textPaint.shader = textShader // 使用缓存的Shader
            textPaint.color = originalColor // 防止干扰Shader
        } else {
            textPaint.shader = null
            textPaint.color = originalColor // 使用原始文本颜色
        }

        canvas.drawText(textToDraw, textX, textY, textPaint)

        // 恢复画笔状态
        textPaint.style = originalStyle
        textPaint.strokeWidth = originalStrokeWidth
        textPaint.shader = originalShader
        textPaint.color = originalColor
    }
} 