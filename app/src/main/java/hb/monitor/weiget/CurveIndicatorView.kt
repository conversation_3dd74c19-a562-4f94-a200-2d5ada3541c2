package hb.monitor.weiget

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.util.SparseArray
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.core.util.forEach
import androidx.core.util.putAll
import androidx.viewpager2.widget.ViewPager2
import androidx.core.util.size

// 更新 IndicatorAdapter 接口
interface IndicatorAdapter {
    fun getCount(): Int

    /**
     * 创建 (或复用) 并绑定指示器视图的静态数据。
     * 此方法应处理视图的初始化和那些不随滚动偏移频繁变化的内容设置。
     * @param position 指示器对应的项目索引。
     * @param convertView 可复用的旧 View。
     * @param parent 父 ViewGroup (CurveIndicatorView 自身)。
     * @return 配置好静态数据的指示器 View。
     */
    fun createAndBindData(position: Int, convertView: View?, parent: ViewGroup): View

    /**
     * 根据当前的滚动状态更新指示器视图的视觉表现。
     * 此方法处理随滚动偏移频繁变化的属性，如透明度、缩放、光晕等。
     * @param view 要更新的指示器 View。
     * @param position 该指示器对应的项目索引。
     * @param selectionProgress 此项的"选中进度"，从 0.0 到 1.0。
     * @param globalScrollOffset ViewPager2 的原始 positionOffset (0 到 1 之间)。
     */
    fun updateViewScrollState(view: View, position: Int, selectionProgress: Float, globalScrollOffset: Float)

    /**
     * (可选) 获取适配器希望的指示器最大高度（像素）。
     * 如果返回 null 或小于等于0的值，CurveIndicatorView 将使用其内部默认值。
     */
    fun getPreferredIndicatorMaxHeightPx(): Int? = null

    // 可选的回调
    fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {}
    fun onPageSelected(position: Int) {}
    fun onPageScrollStateChanged(state: Int) {}
}

class CurveIndicatorView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    // 曲线绘制画笔
    private val curvePaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.STROKE // 描边样式
        color = Color.WHITE // 曲线颜色
    }
    private val curvePath = Path() // 曲线路径
    private var curveGradient: Shader? = null // 曲线渐变效果
    
    // ViewPager2 实例
    private var viewPager: ViewPager2? = null
    // ViewPager2 中的项目数量
    private var itemCount: Int = 0
    // ViewPager2 当前主要显示的页面索引 (滑动开始时左侧页面的索引)
    private var currentPosition: Int = 0
    // ViewPager2 页面滚动的偏移量 (从0到1)
    private var positionOffset: Float = 0f

    // 曲线描边的宽度（像素）
    private var curveStrokeWidthPx: Float
    // 相邻指示器之间的固定间距（像素），投影到X轴上
    private var indicatorSpacingPx: Float 
    // 曲线最低点距离视图底部的间距（像素）
    private var curveBottomMarginPx: Float
    // 曲线边缘的透明度 (0-255)
    private var edgeAlphaValue: Int = 80
    // 当高度为 wrap_content 时，曲线的默认深度（像素）
    private var defaultCurveDepthPx: Float 
    // 当没有适配器时，用于测量的默认指示器高度
    private var defaultIndicatorHeightPx: Float 
    // 内部用于确定绘制范围的指示器数量，例如中心点左右各显示多少个
    private val indicatorsToShowAroundFocus = 1 // 中心点左右各显示 N 个，总共 2*N + 1
    // 新增：用于测量的默认指示器宽度
    private var defaultIndicatorWidthPx: Float

    // 用于绘图计算的缓存值
    private var pathStartX: Float = 0f
    private var pathWidth: Float = 0f
    private var curveEdgeY: Float = 0f
    private var curveControlPointY: Float = 0f

    private var adapter: IndicatorAdapter? = null
    
    // 用于管理当前活跃的指示器视图 (key: item position, value: View)
    private val activeIndicatorViews = SparseArray<View>()
    // 回收池
    private val detachedViews = mutableListOf<View>()

    init {
        val density = resources.displayMetrics.density
        
        curveStrokeWidthPx = 1f * density
        indicatorSpacingPx = 116f * density
        curveBottomMarginPx = 80f * density
        defaultCurveDepthPx = 30f * density
        defaultIndicatorHeightPx = 60f * density // Default height for AT_MOST spec
        defaultIndicatorWidthPx = 60f * density  // Default width for AT_MOST spec
        
        curvePaint.strokeWidth = curveStrokeWidthPx
    }

    fun setAdapter(adapter: IndicatorAdapter?) {
        this.adapter = adapter
        this.itemCount = adapter?.getCount() ?: 0
        recycleAllActiveViews() // 回收旧适配器的所有视图
        updateIndicatorsPositions() // 更新指示器本身的状态和初始位置
        requestLayout() // 请求重新测量和布局，如果尺寸依赖于adapter内容
        invalidate() // 请求重绘
    }
    
    private fun recycleAllActiveViews() {
        activeIndicatorViews.forEach { _, view ->
            removeView(view) // 从ViewGroup中移除
            detachedViews.add(view) // 添加到回收池
        }
        activeIndicatorViews.clear()
    }

    // 当 CurveIndicatorView 本身被 detach 时，清理资源
    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        recycleAllActiveViews()
        detachedViews.clear() // 清空回收池
    }

    fun attachToViewPager(viewPager: ViewPager2?) {
        this.viewPager?.unregisterOnPageChangeCallback(pageChangeCallback)
        this.viewPager = viewPager
        this.itemCount = adapter?.getCount() ?: this.viewPager?.adapter?.itemCount ?: 0
        
        this.viewPager?.let {
            it.registerOnPageChangeCallback(pageChangeCallback)
            updateIndicatorsPositions() // 更新指示器状态和初始位置
            requestLayout()
            invalidate()
        } ?: run {
            itemCount = 0
            updateIndicatorsPositions() // 清理指示器
            requestLayout()
            invalidate()
        }
    }

    private val pageChangeCallback = object : ViewPager2.OnPageChangeCallback() {
        override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
            <EMAIL> = position
            <EMAIL> = positionOffset
            <EMAIL>?.onPageScrolled(position, positionOffset, positionOffsetPixels)
            updateIndicatorsPositions() // 更新指示器位置
        }

        override fun onPageSelected(position: Int) {
            <EMAIL> = position
            <EMAIL> = 0f 
            <EMAIL>?.onPageSelected(position)
            updateIndicatorsPositions()
        }
         override fun onPageScrollStateChanged(state: Int) {
            <EMAIL>?.onPageScrollStateChanged(state)
        }
    }

    private fun updateCurveGradient() {
        // 曲线渐变效果，依赖于视图宽度
        if (width == 0) return 
        curveGradient = LinearGradient(
            0f, 0f, 
            width.toFloat(), 0f, 
            intArrayOf(Color.argb(edgeAlphaValue, 255, 255, 255), Color.WHITE, Color.argb(edgeAlphaValue, 255, 255, 255)),
            floatArrayOf(0f, 0.5f, 1f), 
            Shader.TileMode.CLAMP 
        )
        curvePaint.shader = curveGradient 
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        // 视图尺寸变化时，才需要重新计算曲线路径和渐变
        updateCurveGradient() 
        updatePathAndIndicatorParams() 
        // 清理并重新计算和放置指示器，因为它们的位置依赖于曲线路径
        // recycleAllActiveViews() // 可能不需要完全回收，updateIndicatorsPositions会处理
        updateIndicatorsPositions()
    }
    
    private fun getCurrentMaxIndicatorHeight(): Float {
        val adapterPreferredHeight = adapter?.getPreferredIndicatorMaxHeightPx()
        return if (adapterPreferredHeight != null && adapterPreferredHeight > 0) {
            adapterPreferredHeight.toFloat()
        } else {
            defaultIndicatorHeightPx
        }
    }
    
    private fun updatePathAndIndicatorParams() {
        // 如果视图的宽度或高度为0，则无法进行计算，直接返回
        if (width == 0 || height == 0) return 

        // 计算有效的可绘制宽度，减去左右内边距
        val effectiveDrawableWidth = (width - paddingLeft - paddingRight).toFloat()
        // 如果可绘制宽度小于等于0，重置路径并返回
        if (effectiveDrawableWidth <= 0) {
            pathWidth = 0f 
            curvePath.reset() 
            return
        }

        // 路径的起始X坐标，即视图的左内边距
        pathStartX = paddingLeft.toFloat() 
        // 路径的总宽度，即可绘制区域的宽度
        pathWidth = effectiveDrawableWidth 

        // 曲线在Y轴上的边缘位置（曲线两个端点的Y坐标）
        // 计算时考虑了上内边距和曲线描边宽度的一半，确保曲线完整显示
        curveEdgeY = paddingTop.toFloat() + curveStrokeWidthPx / 2f

        // 获取当前指示器的最大可能高度，用于确保曲线深度足够容纳指示器
        val maxIndicatorHeight = getCurrentMaxIndicatorHeight()
        // 计算可用于绘制曲线的有效垂直空间
        // 从视图总高度减去上下内边距和曲线底部预留间距
        val availableHeightForCurveDrawing = (this.height - paddingTop - paddingBottom - curveBottomMarginPx).toFloat()
        // 计算曲线的实际深度
        // 从可绘制高度中减去曲线描边宽度，并确保至少有指示器高度那么深，以避免指示器超出曲线范围
        val curveDepth = (availableHeightForCurveDrawing - curveStrokeWidthPx).coerceAtLeast(maxIndicatorHeight) 

        // 曲线在最低点的中心线Y坐标
        val curveCenterLineAtLowestPointY = curveEdgeY + curveDepth
        
        // 计算二次贝塞尔曲线的控制点X坐标（曲线水平中心）
        val controlPointX = pathStartX + pathWidth / 2f 
        // 计算二次贝塞尔曲线的控制点Y坐标
        // 这个控制点决定了曲线的弯曲程度和方向，这里设置为向上凸起
        // 公式 2 * curveCenterLineAtLowestPointY - curveEdgeY 确保曲线的顶点在 curveCenterLineAtLowestPointY
        curveControlPointY = 2 * curveCenterLineAtLowestPointY - curveEdgeY 

        // 重置并构建曲线路径
        curvePath.reset() 
        // 移动到曲线起点
        curvePath.moveTo(pathStartX, curveEdgeY) 
        // 使用二次贝塞尔曲线绘制路径，参数分别为：控制点X, 控制点Y, 终点X, 终点Y
        curvePath.quadTo(controlPointX, curveControlPointY, pathStartX + pathWidth, curveEdgeY)
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        // 先让 FrameLayout 执行其默认的测量逻辑，它会测量所有子View
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)

        // 然后，我们根据曲线和指示器的需要，确保高度足够
        val heightMode = MeasureSpec.getMode(heightMeasureSpec)
        var measuredHeight = measuredHeight // 从 super.onMeasure 获取到的高度

        if (heightMode != MeasureSpec.EXACTLY) {
            val maxIndicatorHeight = getCurrentMaxIndicatorHeight()
            val calculatedWrapContentHeight = (paddingTop + paddingBottom + defaultCurveDepthPx + curveStrokeWidthPx + maxIndicatorHeight + curveBottomMarginPx).toInt()
            if (heightMode == MeasureSpec.AT_MOST) {
                measuredHeight = measuredHeight.coerceAtMost(calculatedWrapContentHeight)
            } else { // UNSPECIFIED
                measuredHeight = calculatedWrapContentHeight
            }
        }
        setMeasuredDimension(measuredWidth, measuredHeight)
    }

    override fun dispatchDraw(canvas: Canvas) {
        // 1. 先绘制我们自己的曲线 (在子视图绘制之前)
        if (pathWidth > 0) {
            canvas.drawPath(curvePath, curvePaint)
        }
        // 2. 让 FrameLayout 绘制它的子视图 (即我们的指示器)
        super.dispatchDraw(canvas)
        // 3. 如果有需要在子视图之上绘制的内容，在这里进行
    }

    private fun updateIndicatorsPositions() {
        val localAdapter = adapter ?: return
        if (itemCount == 0 || width == 0 || height == 0 || pathWidth <= 0) {
            recycleAllActiveViews()
            return
        }

        val centerFocusItemFloatIndex = currentPosition + positionOffset
        val firstVisibleIndex = (centerFocusItemFloatIndex - indicatorsToShowAroundFocus - 1).toInt().coerceAtLeast(0)
        val lastVisibleIndex = (centerFocusItemFloatIndex + indicatorsToShowAroundFocus + 1).toInt().coerceAtMost(itemCount - 1)

        val viewsToKeep = SparseArray<View>()

        for (actualItemIndex in firstVisibleIndex..lastVisibleIndex) {
            var indicatorView = activeIndicatorViews.get(actualItemIndex)
            val isNewViewOverall = indicatorView == null // 标记是否是第一次看到这个位置的视图

            if (indicatorView == null) { // 此位置的视图不存在，需要创建或从回收池获取
                val convertView = detachedViews.removeFirstOrNull()
                indicatorView = localAdapter.createAndBindData(actualItemIndex, convertView, this)
                
                // 设置LayoutParams并添加到ViewGroup
                if (indicatorView.layoutParams == null || indicatorView.layoutParams !is LayoutParams){
                    indicatorView.layoutParams = LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT)
                }
                addView(indicatorView)
            } 
            // 如果 indicatorView 存在，我们假设其静态数据已绑定。
            // 如果适配器的 createAndBindData 设计为在 convertView 非空时也更新数据，那也没问题。

            // 计算 selectionProgress
            var selectionProgress = 0f
            if (actualItemIndex == currentPosition && actualItemIndex + 1 < itemCount) {
                selectionProgress = 1f - positionOffset
            } else if (actualItemIndex == currentPosition + 1) {
                selectionProgress = positionOffset
            } else if (actualItemIndex == currentPosition && actualItemIndex == itemCount - 1) {
                selectionProgress = 1f
            } else if (actualItemIndex == currentPosition) { 
                 selectionProgress = 1f
            }

            // 更新视图的滚动相关视觉状态
            localAdapter.updateViewScrollState(indicatorView, actualItemIndex, selectionProgress, positionOffset)
            
            viewsToKeep.put(actualItemIndex, indicatorView)

            // 测量视图
            val childWidthMeasureSpec = MeasureSpec.makeMeasureSpec(pathWidth.toInt(), MeasureSpec.AT_MOST)
            val childHeightMeasureSpec = MeasureSpec.makeMeasureSpec(getCurrentMaxIndicatorHeight().toInt(), MeasureSpec.AT_MOST)
            indicatorView.measure(childWidthMeasureSpec, childHeightMeasureSpec)
            
            // 计算和设置位移
            val itemOffsetFromFocus = actualItemIndex - centerFocusItemFloatIndex
            val indicatorPathX = (pathWidth / 2f) + itemOffsetFromFocus * indicatorSpacingPx
            val t = (indicatorPathX).coerceIn(0f, pathWidth) / pathWidth.coerceAtLeast(1f)
            
            val indicatorCenterOnCurveY = ((1 - t) * (1 - t) + t * t) * curveEdgeY +
                                     2 * t * (1 - t) * curveControlPointY

            val canvasIndicatorCenterX = pathStartX + indicatorPathX
            
            indicatorView.translationX = canvasIndicatorCenterX - indicatorView.measuredWidth / 2f
            indicatorView.translationY = indicatorCenterOnCurveY - indicatorView.measuredHeight / 2f
        }

        // 回收不再需要的视图 (逻辑不变)
        val newActiveIndicatorViews = SparseArray<View>()
        for (i in 0 until viewsToKeep.size) {
            newActiveIndicatorViews.put(viewsToKeep.keyAt(i), viewsToKeep.valueAt(i))
        }

        for (i in 0 until activeIndicatorViews.size) {
            val key = activeIndicatorViews.keyAt(i)
            if (newActiveIndicatorViews.get(key) == null) {
                val viewToRecycle = activeIndicatorViews.valueAt(i)
                removeView(viewToRecycle)
                detachedViews.add(viewToRecycle)
            }
        }
        activeIndicatorViews.clear()
        activeIndicatorViews.putAll(newActiveIndicatorViews)
    }
}