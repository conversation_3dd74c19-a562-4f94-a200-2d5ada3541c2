package hb.monitor.weiget

import android.animation.ValueAnimator
import android.content.Context
import android.content.res.Resources
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.util.Log
import android.util.TypedValue
import android.view.View
import android.view.animation.LinearInterpolator
import androidx.core.content.ContextCompat
import androidx.core.graphics.drawable.DrawableCompat
import androidx.core.graphics.drawable.toBitmap
import hb.monitor.jvmti.R
import hb.utils.Loger
import java.util.Random
import kotlin.math.abs
import kotlin.times

class HeartAnimationView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    // 物理参数
    private val gravity = 0.5f // 大幅增加重力加速度，使重力效果更加明显
    private val damping = 0.98f // 略微减小阻尼系数，减少垂直方向的减速
    private val elasticity = 0.7f // 碰撞弹性系数

    // 配置参数
    var enableBottomCollision = true // 是否启用底部碰撞，默认启用
    var defaultSize = 50f
    var initialHeartSizeDp = 32f // 初始爱心大小，单位dp
    var finalHeartSizeDp = 90f // 最终爱心大小，单位dp

    // 缩放动画参数
    private val scaleDurationMs = 500 // 从初始大小到最终大小的时间为500ms
    private val framesInScaleDuration = (scaleDurationMs / 16.67).toInt() // 假设16.67ms一帧，约60fps

    // 绘制相关
    private val paint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val random = Random()
    private val heartList = mutableListOf<Heart>()
    private var heartBitmap: Bitmap? = null
    private val maxHearts = 40 // 最大爱心数量限制

    // 动画相关
    private val animator = ValueAnimator.ofFloat(0f, 1f).apply {
        duration = 16 // 约60fps
        repeatCount = ValueAnimator.INFINITE
        interpolator = LinearInterpolator()
        addUpdateListener {
            invalidate()
        }
    }

    // 渐变速度计算
    private val framesPerSecond = 60 // 假设60fps
    private val fadeOutDurationMs = 300 // 从不透明到完全透明的时间为300ms
    private val alphaDecayFactor = calculateAlphaDecayFactor(framesPerSecond, fadeOutDurationMs)

    init {
        // 预先加载爱心位图
        prepareHeartBitmap()
    }

    // 计算透明度衰减因子，使其在给定的时间内完成从不透明到完全透明的渐变
    private fun calculateAlphaDecayFactor(fps: Int, durationMs: Int): Float {
        // 计算总帧数
        val totalFrames = fps * durationMs / 1000f
        // 计算每帧的衰减率，使得多次相乘后能从255降到接近0
        return (1 - 1f / totalFrames)
    }

    // 将dp转换为px
    private fun dpToPx(dp: Float): Float {
        return TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_DIP,
            dp,
            Resources.getSystem().displayMetrics
        )
    }

    private fun prepareHeartBitmap() {
        val drawable = ContextCompat.getDrawable(context, R.drawable.ic_star)?.mutate()
        if (drawable != null) {
            val tintedDrawable = DrawableCompat.wrap(drawable)
            heartBitmap =
                tintedDrawable.toBitmap(width = defaultSize.toInt(), height = defaultSize.toInt())
        }
    }

    // 仅在需要时启动动画
    private fun startAnimationIfNeeded() {
        if (!animator.isRunning && heartList.isNotEmpty()) {
            animator.start()
        }
    }

    // 如果没有爱心，停止动画
    private fun stopAnimationIfNoHearts() {
        if (animator.isRunning && heartList.isEmpty()) {
            animator.cancel()
        }
    }

    // 启动动画
    fun start() {
        if (!animator.isRunning && heartList.isNotEmpty()) {
            animator.start()
        }
    }

    // 停止动画
    fun stop() {
        if (animator.isRunning) {
            animator.cancel()
        }
        heartList.clear()
        invalidate()
    }

    // 添加一批新的爱心，允许自定义初始大小
    fun addHearts(x: Float, y: Float, count: Int = 10, initialSizeDp: Float = initialHeartSizeDp) {
        // 限制最大数量，防止性能问题
        if (heartList.size >= maxHearts) {
            // 移除最旧的心形
            val removeCount = minOf(count, heartList.size - maxHearts + count)
            repeat(removeCount) { heartList.removeAt(0) }
        }

        // 计算初始缩放系数和最终缩放系数
        val initialScale = dpToPx(initialSizeDp) / defaultSize
        val finalScale = dpToPx(finalHeartSizeDp) / defaultSize

        repeat(count) {
            // 随机初始速度和旋转
            val vx = (random.nextFloat() * 100) - 50 // 水平方向速度
            val vy = -50 - random.nextFloat() * 15  // 进一步增加初始向上速度，配合更强的重力
            val rotation = random.nextFloat() * 360
            val alpha = 255 // 初始完全不透明

            heartList.add(
                Heart(
                    x = x,
                    y = y,
                    vx = vx,
                    vy = vy,
                    currentScale = initialScale,
                    initialScale = initialScale,
                    finalScale = finalScale,
                    scaleProgress = 0, // 初始进度为0
                    rotation = rotation,
                    alpha = alpha,
                    bottomHitCount = 0,
                    fadeStarted = false
                )
            )
        }

        // 有爱心了，确保动画启动
        startAnimationIfNeeded()
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        if (heartBitmap == null) return

        // 更新所有爱心的位置和状态
        val iterator = heartList.iterator()
        var hasRemovedHearts = false

        while (iterator.hasNext()) {
            val heart = iterator.next()

            // 应用物理效果
            updatePhysics(heart)

            // 更新缩放
            updateScale(heart)

            // 检查是否超出视图边界
            if (heart.y > height * 1.2 || heart.y < -height * 0.2 ||
                heart.x < -width * 0.2 || heart.x > width * 1.2 || heart.alpha <= 5
            ) {
                iterator.remove()
                hasRemovedHearts = true
                continue
            }

            // 如果不启用底部碰撞，且爱心触底，则直接移除
            if (!enableBottomCollision && heart.y + 25 * heart.currentScale >= height) {
                iterator.remove()
                hasRemovedHearts = true
                continue
            }

            // 绘制爱心
            drawHeart(canvas, heart)
        }

        // 如果移除了爱心，检查是否需要停止动画
        if (hasRemovedHearts) {
            stopAnimationIfNoHearts()
        }
    }

    private fun updatePhysics(heart: Heart) {
        // 应用重力
        heart.vy += gravity

        // 水平方向应用阻尼
        heart.vx *= damping

        // 垂直方向应用较小的阻尼，保持重力感
        heart.vy *= 0.99f

        // 更新位置
        heart.x += heart.vx
        heart.y += heart.vy

        // 边界碰撞
        handleBoundaryCollision(heart)

        // 如果已经碰撞底部两次以上并启用了底部碰撞，开始渐变消失
        if (heart.fadeStarted && enableBottomCollision) {
            // 快速减少透明度
            heart.alpha = (heart.alpha * alphaDecayFactor).toInt().coerceAtLeast(0)
        }

        // 更新旋转
        heart.rotation += heart.vx * 0.5f
    }

    private fun updateScale(heart: Heart) {
        // 如果缩放动画还未完成
        if (heart.scaleProgress < framesInScaleDuration) {
            // 更新缩放进度
            heart.scaleProgress++

            // 计算当前缩放比例（线性插值）
            val progress = heart.scaleProgress.toFloat() / framesInScaleDuration
            heart.currentScale =
                heart.initialScale + (heart.finalScale - heart.initialScale) * progress
        }
    }

    private fun handleBoundaryCollision(heart: Heart) {
        val radius = 25 * heart.currentScale  // 近似半径，使用当前缩放值

        // 左右边界碰撞
        if (heart.x - radius < 0) {
            heart.x = radius
            heart.vx = abs(heart.vx) * elasticity
        } else if (heart.x + radius > width) {
            heart.x = width - radius
            heart.vx = -abs(heart.vx) * elasticity
        }

        // 上边界碰撞
        if (heart.y - radius < 0) {
            heart.y = radius
            heart.vy = abs(heart.vy) * elasticity
        }
        // 底部碰撞，只在启用时处理
        else if (enableBottomCollision && heart.y + radius > height) {
            heart.y = height - radius
            heart.vy = -abs(heart.vy) * elasticity * 0.8f // 减小反弹力度，更真实

            // 增加底部碰撞计数
            heart.bottomHitCount++

            // 在第二次碰撞时标记开始衰减
            if (heart.bottomHitCount >= 2 && !heart.fadeStarted) {
                heart.fadeStarted = true
            }
        }
    }

    private fun drawHeart(canvas: Canvas, heart: Heart) {
        heartBitmap?.let { bitmap ->
            paint.alpha = heart.alpha

            val matrix = Matrix()
            matrix.postTranslate(-bitmap.width / 2f, -bitmap.height / 2f)
            matrix.postScale(heart.currentScale, heart.currentScale)  // 使用当前缩放值
            matrix.postRotate(heart.rotation)
            matrix.postTranslate(heart.x, heart.y)

            canvas.drawBitmap(bitmap, matrix, paint)
        }
    }

    // 表示单个爱心的数据类
    data class Heart(
        var x: Float,
        var y: Float,
        var vx: Float,                // x方向速度
        var vy: Float,                // y方向速度
        var currentScale: Float,      // 当前缩放值
        val initialScale: Float,      // 初始缩放值
        val finalScale: Float,        // 最终缩放值
        var scaleProgress: Int,       // 缩放动画进度
        var rotation: Float,
        var alpha: Int,
        var bottomHitCount: Int,      // 底部碰撞次数
        var fadeStarted: Boolean      // 是否开始衰减
    )

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        // 不再自动启动动画，而是在需要时启动
    }

    override fun onDetachedFromWindow() {
        stop()
        super.onDetachedFromWindow()
    }
} 