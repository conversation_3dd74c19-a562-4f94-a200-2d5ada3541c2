package hb.monitor.weiget;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;


import androidx.recyclerview.widget.RecyclerView;
import hb.monitor.jvmti.R;


public class MaxLimitRecyclerView extends RecyclerView {
    private int mMaxHeight;
    private int mMaxWidth;

    public MaxLimitRecyclerView(Context context) {
        super(context);
    }

    public MaxLimitRecyclerView(Context context, AttributeSet attrs) {
        super(context, attrs);
        initialize(context, attrs);
    }

    public MaxLimitRecyclerView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initialize(context, attrs);
    }

    private void initialize(Context context, AttributeSet attrs) {
        TypedArray arr = context.obtainStyledAttributes(attrs, R.styleable.MaxLimitRecyclerView);
        mMaxHeight = arr.getLayoutDimension(R.styleable.MaxLimitRecyclerView_maxHeight, mMaxHeight);
        mMaxWidth = arr.getLayoutDimension(R.styleable.MaxLimitRecyclerView_maxWidth, mMaxWidth);
        arr.recycle();
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        if (mMaxHeight > 0) {
            heightMeasureSpec = MeasureSpec.makeMeasureSpec(mMaxHeight, MeasureSpec.AT_MOST);
        }
        if (mMaxWidth > 0) {
            widthMeasureSpec = MeasureSpec.makeMeasureSpec(mMaxWidth, MeasureSpec.AT_MOST);
        }
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
    }
}