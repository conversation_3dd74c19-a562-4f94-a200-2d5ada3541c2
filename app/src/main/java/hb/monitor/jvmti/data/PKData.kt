package hb.monitor.jvmti.data

/**
 * PK数据模型
 */
data class PKResponse(
    val status: Int, // 状态 0-未开启 1开启未开始-准备中 2-PK进行中 3-已结束
    val remind_time: Int, // 剩余时间，单位秒。准备中、管理员不进行倒计时
    val round_id: String, // 场次id
    val rule: String, // 规则说明
    val hash: String, // 唯一值，用于幂等校验
    val team_pk: TeamPKData?, // 当前是组队pk时下发这个对象
    val multi_player_pk: MultiPlayerPKData? // 多人pk时下发这个对象
) {
    /**
     * 是否是组队PK
     */
    fun isTeamPK(): Boolean = team_pk != null

    /**
     * 是否是多人PK
     */
    fun isMultiPlayerPK(): Boolean = multi_player_pk != null

    /**
     * PK是否已开始
     */
    fun isPKStarted(): Boolean = status == PKStatus.IN_PROGRESS

    /**
     * PK是否在准备中
     */
    fun isPKPreparing(): Boolean = status == PKStatus.PREPARING

    /**
     * PK是否已结束
     */
    fun isPKEnded(): Boolean = status == PKStatus.ENDED
}

/**
 * 组队PK数据
 */
data class TeamPKData(
    val blue_team: TeamData, // 蓝方
    val red_team: TeamData // 红方
)

/**
 * 队伍数据
 */
data class TeamData(
    val total_count: Int, // 总分
    val members: List<PKMember> // 成员列表
)

/**
 * 多人PK数据
 */
data class MultiPlayerPKData(
    val members: List<PKMember> // 成员列表
)

/**
 * PK成员数据
 */
data class PKMember(
    val avatar: String, // 头像
    val uuid: String, // 用户ID
    val is_locked: Int, // 是否锁定 1-是，0-否
    val star_count: Int // 当前星星数
)

/**
 * PK状态枚举
 */
object PKStatus {
    const val NOT_STARTED = 0 // 未开启
    const val PREPARING = 1 // 开启未开始-准备中
    const val IN_PROGRESS = 2 // PK进行中
    const val ENDED = 3 // 已结束
}


