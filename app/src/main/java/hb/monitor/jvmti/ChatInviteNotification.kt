package hb.monitor.jvmti

import android.view.View
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import androidx.core.content.ContextCompat
import hb.monitor.jvmti.R

// 1. 定义数据模型
data class ChatInviteModel(
    val title: String,
    val buttonText: String,
    // 在实际项目中，这里应该是图片URL或者资源ID
    val imageResId: Int
)

// 2. 实现视图绑定器
class ChatInviteBinder : INotificationBinder<ChatInviteModel> {

    override fun getLayoutId(): Int {
        return R.layout.notification_chat_invite
    }

    // 我们可以给这类通知一个较高的优先级
    override fun getPriority(): Int = 10 
    
    // Binder 只负责"声明"超时时长，不负责实现。
    override fun getTimeoutMillis(): Long = 5000

    override fun bind(view: View, model: ChatInviteModel, notificationId: String, isDarkMode: Boolean, hideAction: () -> Unit) {
        val tvTitle: TextView = view.findViewById(R.id.tv_notification_title)
        val btnAction: Button = view.findViewById(R.id.btn_notification_action)
        val ivAvatar: ImageView = view.findViewById(R.id.iv_notification_icon)
        val ivClose: ImageView = view.findViewById(R.id.iv_notification_close)

        tvTitle.text = model.title
        btnAction.text = model.buttonText
        ivAvatar.setImageResource(model.imageResId)

        // 根据深色模式调整UI样式
        if (isDarkMode) {
            // 深色模式下的样式调整
            // 例如：调整文字颜色、背景色等
             tvTitle.setTextColor(ContextCompat.getColor(view.context, hb.xstyle.R.color.TH_Blue100))
            // view.setBackgroundResource(R.drawable.notification_bg_dark)
        } else {
            // 浅色模式下的样式调整
             tvTitle.setTextColor(ContextCompat.getColor(view.context, hb.xstyle.R.color.TH_Pink100))
            // view.setBackgroundResource(R.drawable.notification_bg_light)
        }

        // --- 设置点击事件 ---

        btnAction.setOnClickListener {
            Toast.makeText(view.context, "正在前往围观...", Toast.LENGTH_SHORT).show()
            hideAction()
        }

        ivClose.setOnClickListener {
            hideAction()
        }

        view.setOnClickListener {
            btnAction.performClick()
        }
    }

    // onDismissed 可以留空，因为所有资源管理都在 Manager 中完成。
    // override fun onDismissed(notificationId: String) {}
} 