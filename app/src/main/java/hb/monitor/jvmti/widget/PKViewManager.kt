package hb.monitor.jvmti.widget

import android.content.Context
import android.view.ViewGroup
import android.view.LayoutInflater
import android.view.View
import hb.monitor.jvmti.R

/**
 * PK视图管理器，实现按需创建和切换PK视图
 * 避免同时创建多个复杂视图造成的资源浪费
 */
class PKViewManager(
    private val context: Context,
    private val container: ViewGroup
) {

    // PK状态变化监听器
    interface PKStatusListener {
        fun onPKStarted()
        fun onPKEnded()
    }

    private var pkStatusListener: PKStatusListener? = null
    
    // PK模式枚举
    enum class PKMode {
        TEAM_PK,        // 组队PK
        MULTIPLAYER_PK  // 多人PK
    }
    
    // 当前活动的PK视图
    private var currentPKView: BasePKView? = null
    private var currentMode: PKMode? = null

    /**
     * 切换到指定的PK模式
     * 每次切换都会创建新的视图，确保状态干净
     *
     * @param mode PK模式
     */
    fun switchToPKMode(mode: PKMode) {
        // 如果是相同模式，则不做任何操作
        if (currentMode == mode) {
            return
        }

        // 移除当前视图
        removeCurrentView()

        // 创建新视图
        val newView = when (mode) {
            PKMode.TEAM_PK -> createTeamPKView()
            PKMode.MULTIPLAYER_PK -> createMultiplayerPKView()
        }

        // 添加新视图到容器
        container.addView(newView)

        // 更新当前状态
        currentPKView = newView
        currentMode = mode
    }
    
    /**
     * 创建组队PK视图
     */
    private fun createTeamPKView(): TeamPKView {
        return TeamPKView(context).apply {
            layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
            // 设置默认配置
            maxTeamSize = 8
            // 设置状态监听器
            setPKStatusListener(object : BasePKView.PKStatusListener {
                override fun onPKStarted() {
                    pkStatusListener?.onPKStarted()
                }

                override fun onPKEnded() {
                    pkStatusListener?.onPKEnded()
                }
            })
        }
    }

    /**
     * 创建多人PK视图
     */
    private fun createMultiplayerPKView(): MultiplayerPKView {
        return MultiplayerPKView(context).apply {
            layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
            // 设置默认配置
            maxTeamSize = 20
            // 设置状态监听器
            setPKStatusListener(object : BasePKView.PKStatusListener {
                override fun onPKStarted() {
                    pkStatusListener?.onPKStarted()
                }

                override fun onPKEnded() {
                    pkStatusListener?.onPKEnded()
                }
            })
        }
    }
    
    /**
     * 移除当前视图
     */
    private fun removeCurrentView() {
        currentPKView?.let { view ->
            container.removeView(view)
        }
        currentPKView = null
    }
    
    /**
     * 获取当前PK视图
     */
    fun getCurrentPKView(): BasePKView? = currentPKView
    
    /**
     * 获取当前PK模式
     */
    fun getCurrentMode(): PKMode? = currentMode
    
    /**
     * 检查是否有活动的PK视图
     */
    fun hasActivePKView(): Boolean = currentPKView != null
    
    /**
     * 清理当前视图
     * 在Activity销毁时调用，释放资源
     */
    fun cleanup() {
        removeCurrentView()
        currentMode = null
    }
    
    /**
     * 配置当前PK视图的最大人数
     */
    fun setMaxTeamSize(maxSize: Int) {
        currentPKView?.maxTeamSize = maxSize
    }
    
    /**
     * 获取当前PK视图的最大人数
     */
    fun getMaxTeamSize(): Int? = currentPKView?.maxTeamSize
    
    /**
     * 检查当前PK是否已开始
     */
    fun isPKStarted(): Boolean = currentPKView?.isPKStarted() ?: false

    /**
     * 设置PK状态监听器
     */
    fun setPKStatusListener(listener: PKStatusListener) {
        pkStatusListener = listener
    }

    /**
     * 获取当前状态信息（用于调试）
     */
    fun getCurrentInfo(): String {
        val currentStatus = currentMode?.name ?: "无"
        val hasView = if (currentPKView != null) "已创建" else "未创建"

        return "当前模式: $currentStatus, 视图状态: $hasView"
    }
}
