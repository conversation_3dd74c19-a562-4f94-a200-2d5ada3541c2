package hb.monitor.jvmti.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View.GONE
import android.view.View.VISIBLE
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.LinearLayoutManager
import hb.monitor.jvmti.R
import hb.monitor.jvmti.data.PKMember
import hb.monitor.jvmti.adapter.PKUserAdapter
import hb.monitor.jvmti.databinding.ViewTeamPkBinding
import hb.monitor.jvmti.databinding.IncludePkHeaderBinding
import kotlin.random.Random
import androidx.core.view.isVisible
import android.widget.FrameLayout
import dp
import hb.monitor.jvmti.data.PKResponse
import hb.monitor.jvmti.data.PKStatus

class TeamPKView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : BasePKView(context, attrs, defStyleAttr) {

    private val binding = ViewTeamPkBinding.inflate(LayoutInflater.from(context), this)
    private val headerBinding = IncludePkHeaderBinding.bind(this)

    private val blueTeamUsers = mutableListOf<PKMember>()
    private val redTeamUsers = mutableListOf<PKMember>()

    private lateinit var blueTeamExpandedAdapter: PKUserAdapter
    private lateinit var redTeamExpandedAdapter: PKUserAdapter
    private lateinit var blueTeamCollapsedAdapter: PKUserAdapter
    private lateinit var redTeamCollapsedAdapter: PKUserAdapter

    // 当前队伍总分
    private var blueTeamTotalScore = 0
    private var redTeamTotalScore = 0


    init {
        // 设置渐变背景
        setBackgroundResource(R.drawable.bg_team_pk_gradient)
        setupViews()
        setupHeaderListeners()
        binding.btnToggleExpand.setOnClickListener {
            toggleExpand()
        }
        // 设置初始状态为展开
        setExpanded(true)

        // 设置进度变化监听器
        binding.progressBar.setOnProgressChangeListener(object :
            hb.monitor.jvmti.widget.TeamPKProgressBar.OnProgressChangeListener {
            override fun onProgressChanged(
                blueScore: Int,
                redScore: Int,
                blueWidth: Float,
                totalWidth: Float
            ) {
                updateScoreDisplay(blueScore, redScore)
                updateLightningPosition(blueWidth, totalWidth)
            }
        })
    }

    private fun setupHeaderListeners() {
        // 通过headerBinding访问include文件中的控件
        headerBinding.btnStart.setOnClickListener {
            startPk()
        }
    }

    override fun onMaxTeamSizeChanged() {
        updateAddButtonVisibility()
    }


    private fun setupViews() {
        // 展开适配器（不再需要添加按钮回调，因为按钮现在是固定的）
        blueTeamExpandedAdapter = PKUserAdapter(isExpanded = true, onAddClick = { }, isTeamPK = true)
        redTeamExpandedAdapter = PKUserAdapter(isExpanded = true, onAddClick = { }, isTeamPK = true)

        // 收起适配器（不再需要添加按钮回调，因为按钮现在是固定的）
        blueTeamCollapsedAdapter = PKUserAdapter(isExpanded = false, onAddClick = { }, isTeamPK = true)
        redTeamCollapsedAdapter = PKUserAdapter(isExpanded = false, onAddClick = { }, isTeamPK = true)

        // 设置展开recycleView属性
        binding.blueTeamExpanded.rvUsers.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = blueTeamExpandedAdapter
            // 完全禁用所有动画，包括插入、移除、变化和位置移动动画
            itemAnimator = null
        }
        binding.redTeamExpanded.rvUsers.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = redTeamExpandedAdapter
            // 完全禁用所有动画，包括插入、移除、变化和位置移动动画
            itemAnimator = null
        }

        // 设置收起 recycleView 属性
        binding.blueTeamCollapsed.rvUsers.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            adapter = blueTeamCollapsedAdapter
            // 完全禁用所有动画，包括插入、移除、变化和位置移动动画
            itemAnimator = null
        }
        binding.redTeamCollapsed.rvUsers.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            adapter = redTeamCollapsedAdapter
            // 完全禁用所有动画，包括插入、移除、变化和位置移动动画
            itemAnimator = null
        }

        // 设置标题
        headerBinding.tvTitle.text = "组队PK"

        // 自定义队伍名称和背景
        setupTeamAppearance()

        // 设置不同队伍添加按钮样式
        binding.blueTeamExpanded.ivAddMember.setImageFromResource(R.drawable.hichat_pk_add_member_blue_ic)
        binding.redTeamExpanded.ivAddMember.setImageFromResource(R.drawable.hichat_pk_add_member_red_ic)
        binding.blueTeamCollapsed.ivAddMember.setImageFromResource(R.drawable.hichat_pk_add_member_blue_ic)
        binding.redTeamCollapsed.ivAddMember.setImageFromResource(R.drawable.hichat_pk_add_member_red_ic)

        // 设置固定添加按钮的点击事件
        binding.blueTeamExpanded.ivAddMember.setOnClickListener { addUserToTeam(true) }
        binding.redTeamExpanded.ivAddMember.setOnClickListener { addUserToTeam(false) }
        binding.blueTeamCollapsed.ivAddMember.setOnClickListener { addUserToTeam(true) }
        binding.redTeamCollapsed.ivAddMember.setOnClickListener { addUserToTeam(false) }

        // 初始化列表提交
        updateLists()

        // 初始化进度条
        updateProgressBar()
    }

    private fun startPk() {
        if (blueTeamUsers.isEmpty() || redTeamUsers.isEmpty()) {
            // 不能在队伍为空的情况下开始PK
            return
        }

        // 通过ViewModel更新PK状态为进行中
        pkViewModel?.let { viewModel ->
            val currentData = viewModel.getCurrentPKData()
            if (currentData != null) {
                val startedData = currentData.copy(
                    status = PKStatus.IN_PROGRESS,
                    hash = "${currentData.hash}_started_${System.currentTimeMillis()}"
                )
                viewModel.updatePKData(startedData)
            }
        }

        // 使用基类的开始PK逻辑
        startPkCommon()

        // 创建新的用户列表，使用基类的方法生成分数和排名
        val newBlueUsers = generateScoresAndRanks(blueTeamUsers)
        val newRedUsers = generateScoresAndRanks(redTeamUsers)

        // 用全新的数据更新视图持有的数据源
        this.blueTeamUsers.clear()
        this.blueTeamUsers.addAll(newBlueUsers)
        this.redTeamUsers.clear()
        this.redTeamUsers.addAll(newRedUsers)

        updateLists(isStructuralChange = true)

        // 更新进度条
        updateProgressBar()
    }


    private fun addUserToTeam(isBlueTeam: Boolean) {
        val currentTeam = if (isBlueTeam) blueTeamUsers else redTeamUsers

        // 使用基类的方法检查是否可以添加用户
        if (canAddMoreUsers(currentTeam.size)) {
            // 使用基类的方法创建新用户
            val newUser = createNewUser()

            val newList = ArrayList(currentTeam)
            newList.add(newUser)

            if (isBlueTeam) {
                this.blueTeamUsers.clear()
                this.blueTeamUsers.addAll(newList)
            } else {
                this.redTeamUsers.clear()
                this.redTeamUsers.addAll(newList)
            }
            // 使用结构性变化强制刷新列表
            updateLists(isStructuralChange = true)
        }
    }

    private fun updateLists(isStructuralChange: Boolean = false) {
        // 总是传递列表的副本给适配器
        blueTeamExpandedAdapter.submitList(ArrayList(blueTeamUsers), isStructuralChange)
        redTeamExpandedAdapter.submitList(ArrayList(redTeamUsers), isStructuralChange)
        blueTeamCollapsedAdapter.submitList(ArrayList(blueTeamUsers), isStructuralChange)
        redTeamCollapsedAdapter.submitList(ArrayList(redTeamUsers), isStructuralChange)

        // 更新添加按钮的显示状态
        updateAddButtonVisibility()
    }

    /**
     * 更新添加按钮的显示状态
     * 展开状态：列表为空时显示中间，有用户时显示顶部，满员时隐藏
     * 收起状态：只在PK未开始且队伍未满时显示
     */
    private fun updateAddButtonVisibility() {
        // 使用基类的方法检查是否应该显示添加按钮
        val showBlueAddButton = shouldShowAddButton(blueTeamUsers.size)
        val showRedAddButton = shouldShowAddButton(redTeamUsers.size)

        // 展开状态的添加按钮逻辑
        updateExpandedAddButton(
            binding.blueTeamExpanded.ivAddMember,
            binding.blueTeamExpanded.rvUsers,
            showBlueAddButton,
            blueTeamUsers.isEmpty()
        )
        updateExpandedAddButton(
            binding.redTeamExpanded.ivAddMember,
            binding.redTeamExpanded.rvUsers,
            showRedAddButton,
            redTeamUsers.isEmpty()
        )

        // 收起状态的添加按钮逻辑
        updateCollapsedAddButton(
            binding.blueTeamCollapsed.ivAddMember,
            binding.blueTeamCollapsed.rvUsers,
            showBlueAddButton
        )
        updateCollapsedAddButton(
            binding.redTeamCollapsed.ivAddMember,
            binding.redTeamCollapsed.rvUsers,
            showRedAddButton
        )
    }

    /**
     * 更新展开状态的添加按钮位置和显示
     *
     * @param addButton 添加按钮视图
     * @param recyclerView 对应的RecyclerView
     * @param shouldShow 是否应该显示按钮
     * @param isEmpty 列表是否为空
     */
    private fun updateExpandedAddButton(
        addButton: android.widget.ImageView,
        recyclerView: androidx.recyclerview.widget.RecyclerView,
        shouldShow: Boolean,
        isEmpty: Boolean
    ) {
        if (shouldShow) {
            addButton.visibility = VISIBLE
            val buttonLayoutParams = addButton.layoutParams as FrameLayout.LayoutParams
            val recyclerLayoutParams = recyclerView.layoutParams as FrameLayout.LayoutParams

            if (isEmpty) {
                // 列表为空时，按钮居中显示，RecyclerView保持margin
                buttonLayoutParams.gravity = android.view.Gravity.CENTER
                buttonLayoutParams.topMargin = 0.dp
                recyclerLayoutParams.topMargin = 48.dp
            } else {
                // 有用户时，按钮显示在顶部，RecyclerView保持margin
                buttonLayoutParams.gravity =
                    android.view.Gravity.CENTER_HORIZONTAL or android.view.Gravity.TOP
                buttonLayoutParams.topMargin = 8.dp
                recyclerLayoutParams.topMargin = 48.dp
            }

            addButton.layoutParams = buttonLayoutParams
            recyclerView.layoutParams = recyclerLayoutParams
        } else {
            // 不显示添加按钮（PK开始或满员），RecyclerView顶部对齐
            addButton.visibility = GONE
            val recyclerLayoutParams = recyclerView.layoutParams as FrameLayout.LayoutParams
            recyclerLayoutParams.topMargin = 0
            recyclerView.layoutParams = recyclerLayoutParams
        }
    }

    /**
     * 更新收起状态的添加按钮位置和显示
     *
     * @param addButton 添加按钮视图
     * @param recyclerView 对应的RecyclerView
     * @param shouldShow 是否应该显示按钮
     */
    private fun updateCollapsedAddButton(
        addButton: android.widget.ImageView,
        recyclerView: androidx.recyclerview.widget.RecyclerView,
        shouldShow: Boolean
    ) {
        addButton.isVisible = shouldShow
    }


    /**
     * 获取蓝方当前人数
     */
    fun getBlueTeamSize(): Int = blueTeamUsers.size

    /**
     * 获取红方当前人数
     */
    fun getRedTeamSize(): Int = redTeamUsers.size

    /**
     * 检查蓝方是否已满员
     */
    fun isBlueTeamFull(): Boolean = blueTeamUsers.size >= maxTeamSize

    /**
     * 检查红方是否已满员
     */
    fun isRedTeamFull(): Boolean = redTeamUsers.size >= maxTeamSize

    /**
     * 检查两队是否都已满员
     */
    fun areBothTeamsFull(): Boolean = isBlueTeamFull() && isRedTeamFull()

    /**
     * 获取蓝方总分（用于测试）
     */
    fun getBlueTeamTotalScore(): Int = blueTeamTotalScore

    /**
     * 获取红方总分（用于测试）
     */
    fun getRedTeamTotalScore(): Int = redTeamTotalScore

    /**
     * 获取进度条实例（用于外部更新分数）
     */
    fun getProgressBar(): hb.monitor.jvmti.widget.TeamPKProgressBar = binding.progressBar

    /**
     * 更新分数显示
     */
    private fun updateScoreDisplay(blueScore: Int, redScore: Int) {
        binding.tvScoreLeft.text = blueScore.toString()
        binding.tvScoreRight.text = redScore.toString()
    }

    /**
     * 更新闪电位置到进度条相交处
     */
    private fun updateLightningPosition(blueWidth: Float, totalWidth: Float) {
        val lightningLayoutParams =
            binding.viewLightning.layoutParams as? ConstraintLayout.LayoutParams
        lightningLayoutParams?.let {
            lightningLayoutParams.leftMargin = (blueWidth - binding.viewLightning.width / 2).toInt()
            binding.viewLightning.layoutParams = lightningLayoutParams
        }

    }


    private fun toggleExpand() {
        val isExpanded = binding.blueTeamExpanded.root.isVisible
        setExpanded(!isExpanded)
    }

    private fun setExpanded(isExpanded: Boolean) {
        if (isExpanded) {
            // 显示展开状态的视图
            binding.blueTeamExpanded.root.visibility = VISIBLE
            binding.redTeamExpanded.root.visibility = VISIBLE
            // 隐藏收起状态的视图
            binding.blueTeamCollapsed.root.visibility = GONE
            binding.redTeamCollapsed.root.visibility = GONE
            binding.btnToggleExpand.text = "收起"
        } else {
            // 隐藏展开状态的视图
            binding.blueTeamExpanded.root.visibility = GONE
            binding.redTeamExpanded.root.visibility = GONE
            // 显示收起状态的视图
            binding.blueTeamCollapsed.root.visibility = VISIBLE
            binding.redTeamCollapsed.root.visibility = VISIBLE
            binding.btnToggleExpand.text = "展开"
        }

        // 控制添加按钮的显示（只在PK未开始且队伍未满时显示）
        updateAddButtonVisibility()
    }

    /**
     * 更新进度条显示
     */
    private fun updateProgressBar() {
        // 计算队伍总分
        blueTeamTotalScore = blueTeamUsers.sumOf { it.star_count }
        redTeamTotalScore = redTeamUsers.sumOf { it.star_count }

        // 更新进度条
        binding.progressBar.updateProgress(blueTeamTotalScore, redTeamTotalScore)

        // 设置计时器文本
        val timerText = if (isPkStarted) {
            "进行中 45:00"
        } else {
            "未开始 45:00"
        }
        binding.tvTimer.text = timerText
    }

    /**
     * 设置团队外观（名称和背景）
     */
    private fun setupTeamAppearance() {
        // 设置蓝方背景（右侧圆角）
        binding.blueTeamExpanded.root.setBackgroundResource(R.drawable.bg_blue_team)
        binding.blueTeamCollapsed.root.setBackgroundResource(R.drawable.bg_blue_team)

        // 设置红方背景（左侧圆角）
        binding.redTeamExpanded.root.setBackgroundResource(R.drawable.bg_red_team)
        binding.redTeamCollapsed.root.setBackgroundResource(R.drawable.bg_red_team)
    }

    /**
     * 绑定组队PK数据
     */
    override fun bindPKData(pkData: PKResponse) {
        super.bindPKData(pkData)

        // 只处理组队PK数据
        if (!pkData.isTeamPK()) return

        val teamPK = pkData.team_pk ?: return

        // 更新蓝方队伍数据 - 直接使用PKMember，按分数排序
        val blueMembers = teamPK.blue_team.members.sortedByDescending { it.star_count }

        // 更新红方队伍数据 - 直接使用PKMember，按分数排序
        val redMembers = teamPK.red_team.members.sortedByDescending { it.star_count }

        // 更新内部数据
        blueTeamUsers.clear()
        blueTeamUsers.addAll(blueMembers)
        redTeamUsers.clear()
        redTeamUsers.addAll(redMembers)

        // 更新适配器
        blueTeamExpandedAdapter.submitList(blueMembers)
        redTeamExpandedAdapter.submitList(redMembers)
        blueTeamCollapsedAdapter.submitList(blueMembers)
        redTeamCollapsedAdapter.submitList(redMembers)

        // 更新进度条
        binding.progressBar.updateProgress(
            teamPK.blue_team.total_count,
            teamPK.red_team.total_count
        )

        // 更新添加按钮显示状态
        updateAddButtonVisibility()
    }

    /**
     * PK状态变化处理
     */
    override fun onPKStatusChanged(isStarted: Boolean) {
        super.onPKStatusChanged(isStarted)

        if (isStarted) {
            // PK开始
            headerBinding.btnStart.visibility = GONE
            headerBinding.btnTimeSettings.visibility = GONE

            // 更新适配器状态
            blueTeamExpandedAdapter.isPkStarted = true
            redTeamExpandedAdapter.isPkStarted = true
            blueTeamCollapsedAdapter.isPkStarted = true
            redTeamCollapsedAdapter.isPkStarted = true

            // 设置进度条为PK开始状态
            binding.progressBar.setPkStarted(true)

            // 显示闪电，隐藏计时器
            binding.viewLightning.visibility = VISIBLE
            binding.tvTimer.visibility = GONE
        } else {
            // PK结束
            headerBinding.btnStart.visibility = VISIBLE
            headerBinding.btnTimeSettings.visibility = VISIBLE

            // 更新适配器状态
            blueTeamExpandedAdapter.isPkStarted = false
            redTeamExpandedAdapter.isPkStarted = false
            blueTeamCollapsedAdapter.isPkStarted = false
            redTeamCollapsedAdapter.isPkStarted = false

            // 设置进度条为PK结束状态
            binding.progressBar.setPkStarted(false)

            // 显示计时器，隐藏闪电
            binding.viewLightning.visibility = GONE
            binding.tvTimer.visibility = VISIBLE
        }

        // 更新添加按钮显示状态
        updateAddButtonVisibility()
    }
}