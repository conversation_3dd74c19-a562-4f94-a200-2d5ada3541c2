package hb.monitor.jvmti.widget

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.*
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.animation.LinearInterpolator
import android.widget.FrameLayout
import androidx.core.content.ContextCompat
import hb.monitor.jvmti.R
import hb.monitor.jvmti.databinding.ViewTeamPkProgressBarBinding

/**
 * 组队PK进度条
 * 显示蓝方和红方的分数对比
 */
class TeamPKProgressBar @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    /**
     * 进度变化监听器
     */
    interface OnProgressChangeListener {
        /**
         * 进度变化回调
         * @param blueScore 蓝方分数
         * @param redScore 红方分数
         * @param blueWidth 蓝方进度条宽度（像素）
         * @param totalWidth 总宽度（像素）
         */
        fun onProgressChanged(blueScore: Int, redScore: Int, blueWidth: Float, totalWidth: Float)
    }

    private val binding = ViewTeamPkProgressBarBinding.inflate(LayoutInflater.from(context), this)
    
    private var blueScore = 0
    private var redScore = 0
    private var isPkStarted = false

    // 进度变化监听器
    private var progressChangeListener: OnProgressChangeListener? = null

    // 动画相关
    private var arrowAnimator: ValueAnimator? = null
    private var animationOffset = 0f // 当前动画偏移量

    // 动画参数
    private val arrowMoveDistance = 5.dpToPx() // 每次移动5px
    private val arrowWidth = 17.dpToPx() // 箭头宽度17dp
    private val arrowSpacing = 0.dpToPx() // 箭头间隔9dp
    private val animationDuration = 200L // 200ms

    // 圆角裁剪
    private val cornerRadius = 60.dpToPx().toFloat()
    private val clipPath = Path()
    private val rectF = RectF()

    // 箭头绘制相关
    private val blueArrowDrawable: Drawable? by lazy {
        ContextCompat.getDrawable(context, R.drawable.hichat_progress_intersect_left)
    }
    private val redArrowDrawable: Drawable? by lazy {
        ContextCompat.getDrawable(context, R.drawable.hichat_progress_intersect_right)
    }

    init {
        // 设置初始状态：0分对等
        updateProgress(0, 0)

        // 启用硬件加速以支持圆角裁剪
        setLayerType(View.LAYER_TYPE_HARDWARE, null)
    }
    
    /**
     * 更新进度条显示
     * @param blueScore 蓝方分数
     * @param redScore 红方分数
     */
    fun updateProgress(blueScore: Int, redScore: Int) {
        this.blueScore = blueScore
        this.redScore = redScore

        // 计算进度比例
        val totalScore = blueScore + redScore
        if (totalScore == 0) {
            // 0分对等情况
            binding.viewBlueProgress.layoutParams = (binding.viewBlueProgress.layoutParams as android.widget.LinearLayout.LayoutParams).apply {
                weight = 0.5f
            }
            binding.viewRedProgress.layoutParams = (binding.viewRedProgress.layoutParams as android.widget.LinearLayout.LayoutParams).apply {
                weight = 0.5f
            }
        } else {
            // 根据分数比例调整
            val blueRatio = blueScore.toFloat() / totalScore
            val redRatio = redScore.toFloat() / totalScore

            binding.viewBlueProgress.layoutParams = (binding.viewBlueProgress.layoutParams as android.widget.LinearLayout.LayoutParams).apply {
                weight = blueRatio
            }
            binding.viewRedProgress.layoutParams = (binding.viewRedProgress.layoutParams as android.widget.LinearLayout.LayoutParams).apply {
                weight = redRatio
            }
        }

        // 请求重新布局
        requestLayout()

        // 通知进度变化
        post {
            val progressBarWidth = width.toFloat()
            if (progressBarWidth > 0) {
                val blueWidth = if (totalScore == 0) {
                    progressBarWidth / 2f
                } else {
                    progressBarWidth * (blueScore.toFloat() / totalScore)
                }
                progressChangeListener?.onProgressChanged(blueScore, redScore, blueWidth, progressBarWidth)
            }
        }

        // 只触发重绘，不重启动画
        if (isPkStarted) {
            invalidate()
        }
    }
    

    
    /**
     * 获取蓝方分数
     */
    fun getBlueScore(): Int = blueScore
    
    /**
     * 获取红方分数
     */
    fun getRedScore(): Int = redScore

    /**
     * 设置进度变化监听器
     */
    fun setOnProgressChangeListener(listener: OnProgressChangeListener?) {
        this.progressChangeListener = listener
    }

    /**
     * 设置PK状态
     */
    fun setPkStarted(started: Boolean) {
        isPkStarted = started
        if (started) {
            startArrowAnimation()
        } else {
            stopArrowAnimation()
        }
        // 触发重绘
        invalidate()
    }



    /**
     * 开始箭头动画
     */
    private fun startArrowAnimation() {
        stopArrowAnimation() // 先停止之前的动画

        // 延迟启动动画，等待布局完成
        post {
            // 动画循环：每移动一个完整间距（箭头宽度17dp + 间隔9dp = 26dp）就是一个周期
            val totalSpacing = arrowWidth + arrowSpacing // 17dp + 9dp = 26dp
            val cycleDuration = (totalSpacing.toFloat() / arrowMoveDistance * animationDuration).toLong()

            arrowAnimator = ValueAnimator.ofFloat(0f, totalSpacing.toFloat()).apply {
                duration = cycleDuration
                repeatCount = ValueAnimator.INFINITE
                repeatMode = ValueAnimator.RESTART
                interpolator = LinearInterpolator() // 使用线性插值器

                addUpdateListener { animator ->
                    animationOffset = animator.animatedValue as Float
                    invalidate() // 触发重绘
                }

                start()
            }
        }
    }




    /**
     * 停止箭头动画
     */
    private fun stopArrowAnimation() {
        arrowAnimator?.cancel()
        arrowAnimator = null
    }

    /**
     * dp转px工具方法
     */
    private fun Int.dpToPx(): Int {
        return (this * context.resources.displayMetrics.density).toInt()
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        // 更新裁剪路径
        updateClipPath()
    }

    override fun dispatchDraw(canvas: Canvas) {
        // 应用圆角裁剪
        canvas.save()
        canvas.clipPath(clipPath)
        super.dispatchDraw(canvas)

        // 绘制箭头动画
        if (isPkStarted) {
            drawArrows(canvas)
        }

        canvas.restore()
    }

    /**
     * 绘制箭头跑马灯效果
     */
    private fun drawArrows(canvas: Canvas) {
        val progressBarWidth = width
        if (progressBarWidth <= 0) return

        // 计算蓝方和红方的进度条宽度
        val totalScore = blueScore + redScore
        val blueWidth = if (totalScore == 0) {
            progressBarWidth / 2f
        } else {
            progressBarWidth * (blueScore.toFloat() / totalScore)
        }

        // 绘制蓝方箭头
        drawBlueArrows(canvas, blueWidth)

        // 绘制红方箭头
        drawRedArrows(canvas, progressBarWidth.toFloat(), blueWidth)
    }

    /**
     * 绘制蓝方箭头
     */
    private fun drawBlueArrows(canvas: Canvas, blueWidth: Float) {
        blueArrowDrawable?.let { drawable ->
            val arrowHeight = height

            // 箭头总间距 = 箭头宽度17dp + 间隔9dp = 26dp
            val totalSpacing = arrowWidth + arrowSpacing

            // 计算需要绘制的箭头数量，确保覆盖整个区域
            val totalArrows = ((blueWidth + totalSpacing * 2) / totalSpacing).toInt() + 3

            for (i in 0 until totalArrows) {
                // 每个箭头的基础位置（箭头末尾到下一个箭头头部间距9dp）
                val baseX = (i * totalSpacing).toFloat() - totalSpacing * 2

                // 当前箭头的X位置（加上动画偏移）
                val arrowX = baseX + animationOffset

                // 只绘制在蓝方区域内的箭头部分
                if (arrowX + arrowWidth > 0 && arrowX < blueWidth) {
                    // 计算裁剪区域，确保箭头不会超出蓝方区域
                    val left = maxOf(0f, arrowX).toInt()
                    val right = minOf(blueWidth, arrowX + arrowWidth).toInt()
                    val top = 0
                    val bottom = arrowHeight

                    if (right > left) {
                        canvas.save()
                        canvas.clipRect(left, top, right, bottom)

                        drawable.setBounds(
                            arrowX.toInt(),
                            top,
                            (arrowX + arrowWidth).toInt(),
                            bottom
                        )
                        drawable.draw(canvas)

                        canvas.restore()
                    }
                }
            }
        }
    }

    /**
     * 绘制红方箭头
     */
    private fun drawRedArrows(canvas: Canvas, progressBarWidth: Float, blueWidth: Float) {
        redArrowDrawable?.let { drawable ->
            val arrowHeight = height

            // 箭头总间距 = 箭头宽度17dp + 间隔9dp = 26dp
            val totalSpacing = arrowWidth + arrowSpacing

            // 计算需要绘制的箭头数量
            val redWidth = progressBarWidth - blueWidth
            val totalArrows = ((redWidth + totalSpacing * 2) / totalSpacing).toInt() + 3

            for (i in 0 until totalArrows) {
                // 每个箭头的基础位置（从右边界外开始）
                val baseX = progressBarWidth + totalSpacing * 2 - (i * totalSpacing).toFloat()

                // 当前箭头的X位置（减去动画偏移，向左移动）
                val arrowX = baseX - animationOffset - arrowWidth

                // 只绘制在红方区域内的箭头部分
                if (arrowX + arrowWidth > blueWidth && arrowX < progressBarWidth) {
                    // 计算裁剪区域，确保箭头不会超出红方区域
                    val left = maxOf(blueWidth, arrowX).toInt()
                    val right = minOf(progressBarWidth, arrowX + arrowWidth).toInt()
                    val top = 0
                    val bottom = arrowHeight

                    if (right > left) {
                        canvas.save()
                        canvas.clipRect(left, top, right, bottom)

                        drawable.setBounds(
                            arrowX.toInt(),
                            top,
                            (arrowX + arrowWidth).toInt(),
                            bottom
                        )
                        drawable.draw(canvas)

                        canvas.restore()
                    }
                }
            }
        }
    }

    /**
     * 更新裁剪路径
     */
    private fun updateClipPath() {
        clipPath.reset()
        rectF.set(0f, 0f, width.toFloat(), height.toFloat())
        clipPath.addRoundRect(rectF, cornerRadius, cornerRadius, Path.Direction.CW)
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        stopArrowAnimation()
    }
}
