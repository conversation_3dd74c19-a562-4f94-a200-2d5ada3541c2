package hb.monitor.jvmti.widget

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.*
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.animation.LinearInterpolator
import android.widget.FrameLayout
import androidx.core.content.ContextCompat
import hb.monitor.jvmti.R
import hb.monitor.jvmti.databinding.ViewMultiPkProgressBarBinding

/**
 * 多人PK进度条
 * 特点：
 * 1. 非进行中时显示渐变背景（#737AFF -> #FF47A6）
 * 2. 四个角圆角60dp
 * 3. 没有实际进度功能，纯装饰性
 */
class MultiPKProgressBar @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private val binding: ViewMultiPkProgressBarBinding =
        ViewMultiPkProgressBarBinding.inflate(LayoutInflater.from(context), this, true)

    private var isPkStarted = false

    // 走马灯动画相关
    private var marqueeAnimator: ValueAnimator? = null
    private var animationOffset = 0f

    // 动画参数（参考组队PK的参数）
    private val arrowMoveDistance = 5.dpToPx() // 每次移动5px
    private val arrowWidth = 4.dpToPx() // 箭头宽度4dp
    private val arrowSpacing = 4.dpToPx() // 箭头间距4dp
    private val animationDuration = 200L // 200ms

    // 走马灯箭头drawable
    private val marqueeDrawable: Drawable? by lazy {
        ContextCompat.getDrawable(context, R.drawable.hichat_progress_ffa_intersect)
    }

    // 圆角裁剪
    private val cornerRadius = 60.dpToPx().toFloat()
    private val clipPath = Path()
    private val rectF = RectF()

    init {

        // 设置初始状态
        updateProgressBarState()

        // 启用硬件加速以支持圆角裁剪
        setLayerType(LAYER_TYPE_HARDWARE, null)
    }

    /**
     * 设置PK状态
     * @param started PK是否已开始
     */
    fun setPkStarted(started: Boolean) {
        isPkStarted = started
        updateProgressBarState()

        if (started) {
            startMarqueeAnimation()
        } else {
            stopMarqueeAnimation()
        }
    }

    /**
     * 获取PK状态
     */
    fun isPkStarted(): Boolean = isPkStarted

    /**
     * 更新进度条状态
     */
    private fun updateProgressBarState() {
        if (isPkStarted) {
            // PK进行中时的状态（可以后续扩展）
            binding.progressBackground.setBackgroundResource(R.drawable.bg_multi_pk_progress)
        } else {
            // 非进行中时显示渐变背景
            binding.progressBackground.setBackgroundResource(R.drawable.bg_multi_pk_progress)
        }
    }

    /**
     * 开始走马灯动画
     */
    private fun startMarqueeAnimation() {
        stopMarqueeAnimation() // 先停止之前的动画

        // 延迟启动动画，等待布局完成
        post {
            // 动画循环：每移动一个完整间距（箭头宽度4dp + 间隔4dp = 8dp）就是一个周期
            val totalSpacing = arrowWidth + arrowSpacing // 4dp + 4dp = 8dp
            val cycleDuration = (totalSpacing.toFloat() / arrowMoveDistance * animationDuration).toLong()

            marqueeAnimator = ValueAnimator.ofFloat(0f, totalSpacing.toFloat()).apply {
                duration = cycleDuration
                repeatCount = ValueAnimator.INFINITE
                repeatMode = ValueAnimator.RESTART
                interpolator = LinearInterpolator() // 使用线性插值器

                addUpdateListener { animator ->
                    animationOffset = animator.animatedValue as Float
                    invalidate() // 触发重绘
                }

                start()
            }
        }
    }

    /**
     * 停止走马灯动画
     */
    private fun stopMarqueeAnimation() {
        marqueeAnimator?.cancel()
        marqueeAnimator = null
        animationOffset = 0f
        invalidate()
    }

    override fun dispatchDraw(canvas: Canvas) {
        // 应用圆角裁剪
        canvas.save()

        // 设置圆角裁剪路径
        rectF.set(0f, 0f, width.toFloat(), height.toFloat())
        clipPath.reset()
        clipPath.addRoundRect(rectF, cornerRadius, cornerRadius, Path.Direction.CW)
        canvas.clipPath(clipPath)

        super.dispatchDraw(canvas)

        // 绘制走马灯动画
        if (isPkStarted) {
            drawMarqueeAnimation(canvas)
        }

        canvas.restore()
    }

    /**
     * 绘制走马灯动画
     */
    private fun drawMarqueeAnimation(canvas: Canvas) {
        marqueeDrawable?.let { drawable ->
            val progressBarWidth = width.toFloat()
            val progressBarHeight = height.toFloat()

            if (progressBarWidth <= 0 || progressBarHeight <= 0) return

            // 箭头总间距 = 箭头宽度4dp + 间隔4dp = 8dp
            val totalSpacing = arrowWidth + arrowSpacing

            // 计算需要绘制的箭头数量，确保覆盖整个区域
            val totalArrows = ((progressBarWidth + totalSpacing * 2) / totalSpacing).toInt() + 3

            for (i in 0 until totalArrows) {
                // 每个箭头的基础位置（从左边界外开始，确保从左边界外进入）
                val baseX = (i * totalSpacing).toFloat() - totalSpacing * 2

                // 当前箭头的X位置（加上动画偏移）
                val arrowX = baseX + animationOffset

                // 只绘制在进度条区域内的箭头部分
                if (arrowX + arrowWidth > 0 && arrowX < progressBarWidth) {
                    // 计算裁剪区域，确保箭头不会超出进度条区域
                    val left = maxOf(0f, arrowX).toInt()
                    val right = minOf(progressBarWidth, arrowX + arrowWidth).toInt()
                    val top = 0
                    val bottom = progressBarHeight.toInt()

                    if (right > left) {
                        canvas.save()
                        canvas.clipRect(left, top, right, bottom)

                        drawable.setBounds(
                            arrowX.toInt(),
                            top,
                            (arrowX + arrowWidth).toInt(),
                            bottom
                        )
                        drawable.draw(canvas)

                        canvas.restore()
                    }
                }
            }
        }
    }

    /**
     * 将dp转换为px的工具方法
     */
    private fun Int.dpToPx(): Int {
        return (this * context.resources.displayMetrics.density).toInt()
    }
}
