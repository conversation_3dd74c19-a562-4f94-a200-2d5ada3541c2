package hb.monitor.jvmti.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.recyclerview.widget.LinearLayoutManager
import hb.monitor.jvmti.R
import hb.monitor.jvmti.data.PKMember
import hb.monitor.jvmti.adapter.PKUserAdapter
import hb.monitor.jvmti.databinding.ViewMultiplayerPkBinding
import hb.monitor.jvmti.databinding.IncludePkHeaderBinding
import androidx.core.view.isVisible
import android.widget.FrameLayout
import android.view.View.GONE
import android.view.View.VISIBLE
import dp
import hb.monitor.jvmti.data.PKResponse
import hb.monitor.jvmti.data.PKStatus

class MultiplayerPKView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : BasePKView(context, attrs, defStyleAttr) {

    private val binding = ViewMultiplayerPkBinding.inflate(LayoutInflater.from(context), this)
    private val headerBinding = IncludePkHeaderBinding.bind(this)

    private lateinit var expandedAdapter: PKUserAdapter
    private lateinit var collapsedAdapter: PKUserAdapter

    private val users = mutableListOf<PKMember>()

    init {
        // 设置渐变背景
        setBackgroundResource(R.drawable.bg_multiplayer_pk_gradient)
        setupViews()
        setupListeners()
        // 设置初始状态为展开
        setExpanded(true)
        // 设置默认最大人数为20人
        maxTeamSize = 20
    }

    override fun onMaxTeamSizeChanged() {
        updateAddButtonVisibility()
    }


    private fun setupViews() {
        setupListAppearance()

        // 展开适配器（不再需要添加按钮回调，因为按钮现在是固定的）
        expandedAdapter = PKUserAdapter(isExpanded = true, onAddClick = { }, isTeamPK = false)

        // 收起适配器（不再需要添加按钮回调，因为按钮现在是固定的）
        collapsedAdapter = PKUserAdapter(isExpanded = false, onAddClick = { }, isTeamPK = false)

        // 设置展开recycleView属性
        binding.expandedContent.rvUsers.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = expandedAdapter
            // 完全禁用所有动画，包括插入、移除、变化和位置移动动画
            itemAnimator = null
        }

        // 设置收起 recycleView 属性
        binding.collapsedContent.rvUsers.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            adapter = collapsedAdapter
            // 完全禁用所有动画，包括插入、移除、变化和位置移动动画
            itemAnimator = null
        }

        // 设置标题
        headerBinding.tvTitle.text = "多人PK"

        // 设置固定添加按钮的点击事件
        binding.expandedContent.ivAddMember.setOnClickListener { addUser() }
        binding.collapsedContent.ivAddMember.setOnClickListener { addUser() }

        // 初始化列表提交
        updateLists()
    }

    private fun setupListAppearance() {
        binding.expandedContent.root.setBackgroundResource(R.drawable.bg_multiplayer_team)
        binding.collapsedContent.root.setBackgroundResource(R.drawable.bg_multiplayer_team)
    }

    private fun setupListeners() {
        binding.btnToggleExpand.setOnClickListener { toggleExpand() }

        // 通过headerBinding访问include文件中的控件
        headerBinding.btnStart.setOnClickListener { startPk() }
    }

    private fun startPk() {
        if (users.isEmpty()) {
            // 不能在没有参与者的情况下开始PK
            return
        }

        // 创建新的用户列表，使用基类的方法生成分数和排名
        val newUsers = generateScoresAndRanks(users)

        // 通过ViewModel更新PK状态为进行中，并包含当前用户数据
        pkViewModel?.let { viewModel ->
            val currentData = viewModel.getCurrentPKData()
            if (currentData != null) {
                val startedData = currentData.copy(
                    status = PKStatus.IN_PROGRESS,
                    hash = "${currentData.hash}_started_${System.currentTimeMillis()}",
                    multi_player_pk = currentData.multi_player_pk?.copy(
                        members = newUsers
                    )
                )
                viewModel.updatePKData(startedData)
            }
        }

        // 使用基类的开始PK逻辑
        startPkCommon()

        // 用全新的数据更新视图持有的数据源
        this.users.clear()
        this.users.addAll(newUsers)

        updateLists(isStructuralChange = true)
    }


    private fun addUser() {
        // 使用基类的方法检查是否可以添加用户
        if (canAddMoreUsers(users.size)) {
            // 使用基类的方法创建新用户
            val newUser = createNewUser()

            val newList = ArrayList(users)
            newList.add(newUser)

            this.users.clear()
            this.users.addAll(newList)

            // 更新ViewModel中的数据
            updatePKDataWithCurrentUsers()

            // 使用结构性变化强制刷新列表
            updateLists(isStructuralChange = true)
        }
    }

    private fun toggleExpand() {
        val isExpanded = binding.expandedContent.root.isVisible
        setExpanded(!isExpanded)
    }

    private fun setExpanded(isExpanded: Boolean) {
        // 使用基类的公共方法设置展开状态
        setExpandedCommon(
            isExpanded,
            binding.expandedContent.root,
            binding.collapsedContent.root,
            binding.btnToggleExpand
        )

        // 更新引导视图的约束，确保按钮位置正确
        updateContentBottomGuide(isExpanded)

        // 控制添加按钮的显示
        updateAddButtonVisibility()
    }

    /**
     * 更新内容底部引导视图的约束
     * 确保按钮始终在正确的内容容器下方
     */
    private fun updateContentBottomGuide(isExpanded: Boolean) {
        val constraintSet = androidx.constraintlayout.widget.ConstraintSet()
        constraintSet.clone(this)

        if (isExpanded) {
            // 展开状态：引导视图约束到展开内容下方
            constraintSet.connect(
                binding.contentBottomGuide.id,
                androidx.constraintlayout.widget.ConstraintSet.TOP,
                binding.expandedContent.root.id,
                androidx.constraintlayout.widget.ConstraintSet.BOTTOM
            )
        } else {
            // 收起状态：引导视图约束到收起内容下方
            constraintSet.connect(
                binding.contentBottomGuide.id,
                androidx.constraintlayout.widget.ConstraintSet.TOP,
                binding.collapsedContent.root.id,
                androidx.constraintlayout.widget.ConstraintSet.BOTTOM
            )
        }

        constraintSet.applyTo(this)
    }

    private fun updateLists(isStructuralChange: Boolean = false) {
        // 总是传递列表的副本给适配器
        expandedAdapter.submitList(ArrayList(users), isStructuralChange)
        collapsedAdapter.submitList(ArrayList(users), isStructuralChange)

        // 更新添加按钮的显示状态
        updateAddButtonVisibility()
    }

    /**
     * 更新添加按钮的显示状态
     * 展开状态：列表为空时显示中间，有用户时显示顶部，满员时隐藏
     * 收起状态：只在PK未开始且未满时显示
     */
    private fun updateAddButtonVisibility() {
        // 使用基类的方法检查是否应该显示添加按钮
        val showAddButton = shouldShowAddButton(users.size)

        // 展开状态的添加按钮逻辑
        updateExpandedAddButton(
            binding.expandedContent.ivAddMember,
            binding.expandedContent.rvUsers,
            showAddButton,
            users.isEmpty()
        )

        // 收起状态的添加按钮逻辑
        updateCollapsedAddButton(
            binding.collapsedContent.ivAddMember,
            binding.collapsedContent.rvUsers,
            showAddButton
        )
    }

    /**
     * 更新展开状态的添加按钮位置和显示
     */
    private fun updateExpandedAddButton(
        addButton: android.widget.ImageView,
        recyclerView: androidx.recyclerview.widget.RecyclerView,
        shouldShow: Boolean,
        isEmpty: Boolean
    ) {
        if (shouldShow) {
            addButton.visibility = VISIBLE
            val buttonLayoutParams = addButton.layoutParams as FrameLayout.LayoutParams
            val recyclerLayoutParams = recyclerView.layoutParams as FrameLayout.LayoutParams

            if (isEmpty) {
                // 列表为空时，按钮居中显示，RecyclerView保持margin
                buttonLayoutParams.gravity = android.view.Gravity.CENTER
                buttonLayoutParams.topMargin = 0.dp
                recyclerLayoutParams.topMargin = 48.dp
            } else {
                // 有用户时，按钮显示在顶部，RecyclerView保持margin
                buttonLayoutParams.gravity =
                    android.view.Gravity.CENTER_HORIZONTAL or android.view.Gravity.TOP
                buttonLayoutParams.topMargin = 8.dp
                recyclerLayoutParams.topMargin = 48.dp
            }

            addButton.layoutParams = buttonLayoutParams
            recyclerView.layoutParams = recyclerLayoutParams
        } else {
            // 不显示添加按钮（PK开始或满员），RecyclerView顶部对齐
            addButton.visibility = GONE
            val recyclerLayoutParams = recyclerView.layoutParams as FrameLayout.LayoutParams
            recyclerLayoutParams.topMargin = 0
            recyclerView.layoutParams = recyclerLayoutParams
        }
    }

    /**
     * 更新收起状态的添加按钮位置和显示
     */
    private fun updateCollapsedAddButton(
        addButton: android.widget.ImageView,
        recyclerView: androidx.recyclerview.widget.RecyclerView,
        shouldShow: Boolean
    ) {
        addButton.isVisible = shouldShow
    }

    /**
     * 获取当前参与者数量
     */
    fun getUserCount(): Int = users.size

    /**
     * 检查是否已满员
     */
    fun isFull(): Boolean = users.size >= maxTeamSize

    /**
     * 获取所有参与者
     */
    fun getUsers(): List<PKMember> = users.toList()

    /**
     * 绑定多人PK数据
     */
    override fun bindPKData(pkData: PKResponse) {
        super.bindPKData(pkData)

        // 只处理多人PK数据
        if (!pkData.isMultiPlayerPK()) return

        val multiPlayerPK = pkData.multi_player_pk ?: return

        // 更新成员数据 - 直接使用PKMember，按分数排序
        val members = multiPlayerPK.members.sortedByDescending { it.star_count }

        // 更新内部数据
        users.clear()
        users.addAll(members)

        // 更新适配器
        expandedAdapter.submitList(members)
        collapsedAdapter.submitList(members)

        // 更新添加按钮显示状态
        updateAddButtonVisibility()
    }

    /**
     * PK状态变化处理
     */
    override fun onPKStatusChanged(isStarted: Boolean) {
        super.onPKStatusChanged(isStarted)

        if (isStarted) {
            // PK开始
            headerBinding.btnStart.visibility = GONE
            headerBinding.btnTimeSettings.visibility = GONE

            // 设置进度条为PK开始状态
            binding.progressBar.setPkStarted(true)

            // 隐藏计时器（PK开始后不显示计时器）
            binding.tvTimer.visibility = GONE

            // 更新适配器状态
            expandedAdapter.isPkStarted = true
            collapsedAdapter.isPkStarted = true
        } else {
            // PK结束
            headerBinding.btnStart.visibility = VISIBLE
            headerBinding.btnTimeSettings.visibility = VISIBLE

            // 设置进度条为PK结束状态
            binding.progressBar.setPkStarted(false)

            // 显示计时器
            binding.tvTimer.visibility = VISIBLE

            // 更新适配器状态
            expandedAdapter.isPkStarted = false
            collapsedAdapter.isPkStarted = false
        }

        // 更新添加按钮显示状态
        updateAddButtonVisibility()
    }

    /**
     * 更新ViewModel中的PK数据，包含当前用户
     */
    private fun updatePKDataWithCurrentUsers() {
        pkViewModel?.let { viewModel ->
            val currentData = viewModel.getCurrentPKData()
            if (currentData?.multi_player_pk != null) {
                val updatedData = currentData.copy(
                    hash = "${currentData.hash}_users_updated_${System.currentTimeMillis()}",
                    multi_player_pk = currentData.multi_player_pk.copy(
                        members = users.toList()
                    )
                )
                viewModel.updatePKData(updatedData)
            }
        }
    }
}