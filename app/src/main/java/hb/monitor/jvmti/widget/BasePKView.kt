package hb.monitor.jvmti.widget

import android.content.Context
import android.util.AttributeSet
import android.view.View.GONE
import android.view.View.VISIBLE
import androidx.constraintlayout.widget.ConstraintLayout
import hb.monitor.jvmti.R
import hb.monitor.jvmti.data.PKResponse
import hb.monitor.jvmti.data.PKMember
import hb.monitor.jvmti.viewmodel.PKViewModel
import kotlin.random.Random

/**
 * PK视图的基类，封装公共的PK逻辑
 */
abstract class BasePKView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    // 可配置的最大人数，默认为10人
    var maxTeamSize: Int = 10
        set(value) {
            field = if (value > 0) value else 10
            // 当设置新的最大人数时，更新按钮显示状态
            onMaxTeamSizeChanged()
        }

    // PK是否已开始
    protected var isPkStarted = false

    // 用于生成唯一的用户ID
    protected var nextUserId = 1

    /**
     * 当最大人数改变时的回调，子类可以重写来更新UI
     */
    protected open fun onMaxTeamSizeChanged() {
        // 默认实现为空，子类可以重写
    }

    // PK状态变化监听器
    private var pkStatusListener: PKStatusListener? = null

    // ViewModel引用
    protected var pkViewModel: PKViewModel? = null

    interface PKStatusListener {
        fun onPKStarted()
        fun onPKEnded()
    }

    /**
     * 设置PK状态监听器
     */
    fun setPKStatusListener(listener: PKStatusListener) {
        pkStatusListener = listener
    }

    /**
     * 设置PKViewModel引用
     */
    fun setPKViewModel(viewModel: PKViewModel?) {
        this.pkViewModel = viewModel
    }

    /**
     * 开始PK的公共逻辑
     */
    protected fun startPkCommon() {
        isPkStarted = true
        // 通知状态变化
        pkStatusListener?.onPKStarted()
    }



    /**
     * 创建新用户的公共逻辑
     */
    protected fun createNewUser(): PKMember {
        return PKMember(
            avatar = "",
            uuid = "user_${nextUserId++}",
            is_locked = 0,
            star_count = 0
        )
    }

    /**
     * 为用户列表生成随机分数和排名
     */
    protected fun generateScoresAndRanks(users: List<PKMember>): List<PKMember> {
        return users.map { it.copy(star_count = Random.nextInt(100, 10000)) }
            .sortedByDescending { it.star_count }
    }

    /**
     * 检查是否可以添加更多用户
     */
    protected fun canAddMoreUsers(currentSize: Int): Boolean {
        return !isPkStarted && currentSize < maxTeamSize
    }



    /**
     * 检查PK是否已开始（公共访问方法）
     */
    fun isPKStarted(): Boolean = isPkStarted


    /**
     * 展开/收起的公共逻辑
     */
    protected fun toggleExpandCommon(
        isCurrentlyExpanded: Boolean,
        onSetExpanded: (Boolean) -> Unit
    ) {
        onSetExpanded(!isCurrentlyExpanded)
    }

    /**
     * 设置展开状态的公共逻辑
     */
    protected fun setExpandedCommon(
        isExpanded: Boolean,
        expandedView: android.view.View,
        collapsedView: android.view.View,
        toggleButton: android.widget.TextView
    ) {
        if (isExpanded) {
            expandedView.visibility = VISIBLE
            collapsedView.visibility = GONE
            toggleButton.text = "收起"
        } else {
            expandedView.visibility = GONE
            collapsedView.visibility = VISIBLE
            toggleButton.text = "展开"
        }
    }

    /**
     * 更新添加按钮可见性的公共逻辑
     */
    protected fun shouldShowAddButton(currentSize: Int): Boolean {
        return !isPkStarted && currentSize < maxTeamSize
    }

    /**
     * 测试分数更新的公共逻辑
     */
    protected fun updateRandomUserScore(users: MutableList<PKMember>): List<PKMember> {
        if (!isPkStarted || users.isEmpty()) return users

        // 随机选择一个用户加分
        val userToUpdate = users.random()
        val updatedUser = userToUpdate.copy(star_count = userToUpdate.star_count + Random.nextInt(50, 200))
        val index = users.indexOf(userToUpdate)
        if (index != -1) {
            users[index] = updatedUser
        }

        // 重新排序
        return users.sortedByDescending { it.star_count }
    }

    /**
     * 批量更新所有用户分数的公共逻辑
     */
    protected fun batchUpdateAllUsersScores(users: List<PKMember>): List<PKMember> {
        if (!isPkStarted || users.isEmpty()) return users

        return users.map { user ->
            user.copy(star_count = user.star_count + Random.nextInt(10, 100))
        }.sortedByDescending { it.star_count }
    }

    /**
     * 绑定PK数据，子类应该重写此方法来处理数据变化
     */
    open fun bindPKData(pkData: PKResponse) {
        // 更新PK状态
        val wasStarted = isPkStarted
        isPkStarted = pkData.isPKStarted()

        // 如果状态发生变化，触发状态变化处理
        if (wasStarted != isPkStarted) {
            onPKStatusChanged(isPkStarted)
        }
    }

    /**
     * PK状态变化时的处理，子类可以重写
     */
    protected open fun onPKStatusChanged(isStarted: Boolean) {
        // 默认实现为空，子类可以重写
    }
}
