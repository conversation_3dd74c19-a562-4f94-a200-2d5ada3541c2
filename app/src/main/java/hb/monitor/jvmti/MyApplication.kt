package hb.monitor.jvmti

import android.app.Application
import android.content.Context
import hb.common.CommonApp
import hb.common.xstatic.EventBusImpl
import hb.xstatic.XStatic
import hb.xstatic.tools.XEventBus

class MyApplication : Application() {

    override fun attachBaseContext(base: Context?) {
        super.attachBaseContext(base)
        CommonApp.getInstance().attachBaseContext(this)
    }

    override fun onCreate() {
        super.onCreate()
        CommonApp.getInstance().onCreate()
        XStatic.initEventBus(EventBusImpl())

        // 1. 初始化应用内通知管理器
        InAppNotificationManager.init(this)


        // 3. 注册我们定义的通知类型
        // 我们用 ID=1 来代表聊天邀请通知
        InAppNotificationManager.registerBinder(1, ChatInviteBinder())

        // 你可以在这里注册更多其他类型的通知
        // InAppNotificationManager.registerBinder(2, AnotherTypeOfBinder())
    }
} 