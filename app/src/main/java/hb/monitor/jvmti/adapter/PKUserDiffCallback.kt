package hb.monitor.jvmti.adapter

import android.os.Bundle
import androidx.recyclerview.widget.DiffUtil
import hb.monitor.jvmti.data.PKMember

const val PAYLOAD_SCORE_CHANGE = "payload_score_change"
const val PAYLOAD_RANK_CHANGE = "payload_rank_change"

class PKUserDiffCallback(
    private val oldList: List<PKMember>,
    private val newList: List<PKMember>
) : DiffUtil.Callback() {

    override fun getOldListSize(): Int = oldList.size
    override fun getNewListSize(): Int = newList.size

    override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        // 用唯一ID判断是否是同一个Item
        return oldList[oldItemPosition].uuid == newList[newItemPosition].uuid
    }

    override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        // 判断内容是否有变化
        val oldUser = oldList[oldItemPosition]
        val newUser = newList[newItemPosition]
        return oldUser.uuid == newUser.uuid &&
                oldUser.star_count == newUser.star_count &&
                oldUser.is_locked == newUser.is_locked
    }

    override fun getChangePayload(oldItemPosition: Int, newItemPosition: Int): Any? {
        // 添加边界检查，防止IndexOutOfBoundsException
        if (oldItemPosition >= oldList.size || newItemPosition >= newList.size) {
            return null
        }

        val oldUser = oldList[oldItemPosition]
        val newUser = newList[newItemPosition]
        val payload = Bundle()

        if (oldUser.star_count != newUser.star_count) {
            payload.putBoolean(PAYLOAD_SCORE_CHANGE, true)
        }
        // 排名变化通过position变化来检测
        if (oldItemPosition != newItemPosition) {
            payload.putBoolean(PAYLOAD_RANK_CHANGE, true)
        }

        return if (payload.isEmpty) null else payload
    }
} 