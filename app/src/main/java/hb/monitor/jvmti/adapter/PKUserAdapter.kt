package hb.monitor.jvmti.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import hb.monitor.jvmti.R
import hb.monitor.jvmti.data.PKMember
import hb.monitor.jvmti.databinding.ItemPkUserCollapsedBinding
import hb.monitor.jvmti.databinding.ItemPkUserCollapsedStartedBinding
import hb.monitor.jvmti.databinding.ItemPkUserExpandedBinding
import hb.monitor.jvmti.databinding.ItemPkUserExpandedStartedBinding
import android.os.Bundle
import androidx.recyclerview.widget.DiffUtil
import android.animation.ObjectAnimator
import android.animation.AnimatorSet
import android.animation.Animator
import android.animation.AnimatorListenerAdapter

class PKUserAdapter(
    private val isExpanded: Boolean,
    private val onAddClick: () -> Unit,
    private val isTeamPK: Boolean = true // 默认为组队PK
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    private val users = mutableListOf<PKMember>()
    var isPkStarted = false

    companion object {
        private const val VIEW_TYPE_USER = 0
        private const val VIEW_TYPE_ADD_BUTTON = 1
        private const val VIEW_TYPE_USER_STARTED = 2
    }

    fun submitList(userList: List<PKMember>, isStructuralChange: Boolean = false) {
        // 当PK开始且为收起状态时，我们只关心第一个用户。
        // 直接裁剪列表以确保数据一致性。
        val targetList = if (isPkStarted && !isExpanded && userList.isNotEmpty()) {
            listOf(userList.first())
        } else {
            // 确保每个用户的ID是唯一的，防止复用问题
            userList.distinctBy { it.uuid }
        }

        // 检查是否有实际变化，避免不必要的更新
        if (users == targetList) {
            return
        }

        if (isStructuralChange || this.users.isEmpty() || targetList.isEmpty()) {
            // 结构性变化或空列表时直接刷新，避免DiffCallback问题
            this.users.clear()
            this.users.addAll(targetList)
            notifyDataSetChanged()
        } else {
            try {
                val diffCallback = PKUserDiffCallback(this.users, targetList)
                val diffResult = DiffUtil.calculateDiff(diffCallback, true)
                this.users.clear()
                this.users.addAll(targetList)
                diffResult.dispatchUpdatesTo(this)
            } catch (e: IndexOutOfBoundsException) {
                // 如果DiffCallback出现问题，回退到直接刷新
                this.users.clear()
                this.users.addAll(targetList)
                notifyDataSetChanged()
            }
        }
    }

    override fun getItemViewType(position: Int): Int {
        return if (isPkStarted) VIEW_TYPE_USER_STARTED else VIEW_TYPE_USER
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        return when (viewType) {
            VIEW_TYPE_USER_STARTED -> {
                if (isExpanded) {
                    val binding = ItemPkUserExpandedStartedBinding.inflate(inflater, parent, false)
                    UserExpandedStartedViewHolder(binding)
                } else {
                    val binding = ItemPkUserCollapsedStartedBinding.inflate(inflater, parent, false)
                    UserCollapsedStartedViewHolder(binding)
                }
            }
            VIEW_TYPE_USER -> {
                if (isExpanded) {
                    val binding = ItemPkUserExpandedBinding.inflate(inflater, parent, false)
                    UserExpandedViewHolder(binding)
                } else {
                    val binding = ItemPkUserCollapsedBinding.inflate(inflater, parent, false)
                    UserCollapsedViewHolder(binding)
                }
            }

            else -> throw IllegalArgumentException("无效的视图类型")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int, payloads: MutableList<Any>) {
        if (payloads.isEmpty()) {
            super.onBindViewHolder(holder, position, payloads)
            return
        }

        val bundle = payloads[0] as Bundle
        val user = users[position]

        when(holder) {
            is UserExpandedStartedViewHolder -> {
                if (bundle.containsKey(PAYLOAD_RANK_CHANGE)) {
                    // 名次变化时添加淡出淡入动画 - 使用position+1作为排名
                    animateTextChange(holder.binding.tvRank, (position + 1).toString())
                } else {
                    holder.binding.tvRank.text = (position + 1).toString()
                }
                if (bundle.containsKey(PAYLOAD_SCORE_CHANGE)) {
                    // 分数变化时添加淡出淡入动画
                    animateTextChange(holder.binding.tvScore, user.star_count.toString())
                } else {
                    holder.binding.tvScore.text = user.star_count.toString()
                }
            }
            is UserCollapsedStartedViewHolder -> {
                if (bundle.containsKey(PAYLOAD_RANK_CHANGE)) {
                    // 名次变化时添加淡出淡入动画 - 使用position+1作为排名
                    animateTextChange(holder.binding.tvRank, (position + 1).toString())
                } else {
                    holder.binding.tvRank.text = (position + 1).toString()
                }
                if (bundle.containsKey(PAYLOAD_SCORE_CHANGE)) {
                    // 分数变化时添加淡出淡入动画
                    animateTextChange(holder.binding.tvScore, user.star_count.toString())
                } else {
                    holder.binding.tvScore.text = user.star_count.toString()
                }
            }
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is UserExpandedStartedViewHolder -> {
                val user = users[position]
                // PKMember没有rank字段，需要根据position计算
                holder.binding.tvRank.text = (position + 1).toString()
                holder.binding.tvName.text = "用户${user.uuid.takeLast(4)}"
                holder.binding.tvScore.text = user.star_count.toString()
                // 暂时使用默认头像，实际应该根据avatar URL加载
                holder.binding.ivAvatar.setImageResource(android.R.drawable.ic_menu_gallery)

                // 根据PK类型应用不同样式
                applyExpandedStartedStyle(holder.binding)
            }
            is UserCollapsedStartedViewHolder -> {
                if (users.isNotEmpty()) {
                    val user = users[position] // 使用position确保正确的索引
                    holder.binding.tvRank.text = (position + 1).toString()
                    holder.binding.tvName.text = "用户${user.uuid.takeLast(4)}"
                    holder.binding.tvScore.text = user.star_count.toString()
                    holder.binding.ivAvatar.setImageResource(android.R.drawable.ic_menu_gallery)
                }
            }
            is UserExpandedViewHolder -> {
                // PK未开始时，直接使用position作为用户索引
                if (position >= 0 && position < users.size) {
                    val user = users[position]
                    // 使用用户的唯一ID作为标识，确保视图正确绑定
                    holder.itemView.tag = user.uuid
                    holder.binding.tvName.text = "用户${user.uuid.takeLast(4)}"
                    holder.binding.ivAvatar.setImageResource(android.R.drawable.ic_menu_gallery)
                } else {
                    // 清除可能的旧数据，防止ViewHolder复用时显示错误内容
                    holder.itemView.tag = null
                    holder.binding.tvName.text = ""
                    holder.binding.ivAvatar.setImageResource(R.mipmap.ic_launcher_round)
                }
            }
            is UserCollapsedViewHolder -> {
                // PK未开始时，直接使用position作为用户索引
                if (position >= 0 && position < users.size) {
                    val user = users[position]
                    // 使用用户的唯一ID作为标识，确保视图正确绑定
                    holder.itemView.tag = user.uuid
                    holder.binding.ivAvatar.setImageResource(android.R.drawable.ic_menu_gallery)
                } else {
                    // 清除可能的旧数据，防止ViewHolder复用时显示错误内容
                    holder.itemView.tag = null
                    holder.binding.ivAvatar.setImageResource(R.mipmap.ic_launcher_round)
                }
            }
        }
    }

    override fun getItemCount(): Int {
        // 现在添加按钮是固定在布局中的，所以列表大小就是实际用户数
        return users.size
    }

    class UserExpandedViewHolder(val binding: ItemPkUserExpandedBinding) : RecyclerView.ViewHolder(binding.root)
    class UserCollapsedViewHolder(val binding: ItemPkUserCollapsedBinding) : RecyclerView.ViewHolder(binding.root)
    class UserExpandedStartedViewHolder(val binding: ItemPkUserExpandedStartedBinding) : RecyclerView.ViewHolder(binding.root)
    class UserCollapsedStartedViewHolder(val binding: ItemPkUserCollapsedStartedBinding) : RecyclerView.ViewHolder(binding.root)

    /**
     * 为文本变化添加淡出淡入动画效果
     *
     * @param textView 需要动画效果的TextView
     * @param newText 新的文本内容
     */
    private fun animateTextChange(textView: android.widget.TextView, newText: String) {
        // 创建淡出动画
        val fadeOut = ObjectAnimator.ofFloat(textView, "alpha", 1f, 0f)
        fadeOut.duration = 300 // 淡出动画持续150毫秒

        // 创建淡入动画
        val fadeIn = ObjectAnimator.ofFloat(textView, "alpha", 0f, 1f)
        fadeIn.duration = 300 // 淡入动画持续150毫秒

        // 创建动画集合，按顺序播放
        val animatorSet = AnimatorSet()
        animatorSet.playSequentially(fadeOut, fadeIn)

        // 在淡出动画结束后更新文本内容
        fadeOut.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                textView.text = newText
            }
        })

        // 开始播放动画
        animatorSet.start()
    }

    /**
     * 为展开状态的进行中item应用样式
     */
    private fun applyExpandedStartedStyle(binding: ItemPkUserExpandedStartedBinding) {
        val context = binding.root.context

        if (isTeamPK) {
            // 组队PK样式
            // item高度36dp
            val layoutParams = binding.root.layoutParams
            layoutParams.height = (36 * context.resources.displayMetrics.density).toInt()
            binding.root.layoutParams = layoutParams

            // rank文本尺寸N3 (12sp)
            binding.tvRank.textSize = 12f

            // 头像大小22dp
            val avatarSize = (22 * context.resources.displayMetrics.density).toInt()
            val avatarLayoutParams = binding.ivAvatar.layoutParams
            avatarLayoutParams.width = avatarSize
            avatarLayoutParams.height = avatarSize
            binding.ivAvatar.layoutParams = avatarLayoutParams

            // 名称尺寸N3 (12sp)
            binding.tvName.textSize = 12f

            // 分数尺寸8sp
            binding.tvScore.textSize = 8f

            // 星星图标8dp
            val starSize = (8 * context.resources.displayMetrics.density).toInt()
            val starLayoutParams = binding.ivStar.layoutParams
            starLayoutParams.width = starSize
            starLayoutParams.height = starSize
            binding.ivStar.layoutParams = starLayoutParams

        } else {
            // 多人PK样式
            // item高度48dp
            val layoutParams = binding.root.layoutParams
            layoutParams.height = (48 * context.resources.displayMetrics.density).toInt()
            binding.root.layoutParams = layoutParams

            // rank尺寸B4 (18sp)
            binding.tvRank.textSize = 18f

            // 头像大小保持默认或设置为更大尺寸
            // 这里可以根据需要调整头像大小

            // 昵称N1 (16sp)
            binding.tvName.textSize = 16f

            // 分数N2 (14sp)
            binding.tvScore.textSize = 14f

            // 星星图标12dp
            val starSize = (12 * context.resources.displayMetrics.density).toInt()
            val starLayoutParams = binding.ivStar.layoutParams
            starLayoutParams.width = starSize
            starLayoutParams.height = starSize
            binding.ivStar.layoutParams = starLayoutParams
        }
    }
}