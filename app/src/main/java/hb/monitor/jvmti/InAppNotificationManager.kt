package hb.monitor.jvmti

import android.app.Activity
import android.app.Application
import android.content.res.Configuration
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import hb.utils.BarUtils
import hb.utils.Loger
import hb.xstatic.tools.XEventBus
import java.lang.ref.WeakReference
import java.util.*

/**
 * 应用内通知管理器
 * 这是一个单例，负责在应用顶层显示和管理通知视图。
 */
object InAppNotificationManager : Application.ActivityLifecycleCallbacks {

    private const val TAG = "InAppNotificationManager"

    // --- 私有属性 ---

    private val binders = mutableMapOf<Int, INotificationBinder<Any>>()

    private var currentActivity: WeakReference<Activity>? = null

    // 使用 WeakHashMap 来为每个 Activity 存储其专属的容器，防止内存泄漏。
    private val containers = WeakHashMap<Activity, FrameLayout>()

    private val activeNotifications = mutableListOf<NotificationData>()
    private val activeViews = mutableMapOf<String, View>()

    private data class NotificationData(val id: String, val type: Int, val model: Any, val priority: Int, val expirationTime: Long?)

    // 通过重写 dispatchMessage，统一监听 Handler 的任务执行。
    private val timeoutHandler = object : Handler(Looper.getMainLooper()) {
        override fun dispatchMessage(msg: Message) {
            // 这里可以验证当前handler是否存在积压情况。
//            Log.d(TAG, "当前消息执行 ${msg.callback}. ")
            super.dispatchMessage(msg)
        }
    }
    private val timeoutRunnables = mutableMapOf<String, Runnable>()

    // 主题相关状态
    private var currentDarkMode: Boolean = false

    // --- 公共 API ---

    /**
     * 初始化管理器，必须在 Application.onCreate 中调用。
     * @param app Application 实例
     */
    fun init(app: Application) {
        app.registerActivityLifecycleCallbacks(this)
        registerThemeChangeListener()
    }

    /**
     * 注册 EventBus 监听换肤事件
     * 在需要监听换肤的地方调用此方法
     */
    fun registerThemeChangeListener() {
        XEventBus.register(this)
    }

    /**
     * 注销 EventBus 监听
     * 在不需要监听时调用，防止内存泄漏
     */
    fun unregisterThemeChangeListener() {
        XEventBus.unregster(this)
    }

    /**
     * 注册一个通知类型及其对应的视图绑定器。
     * @param type 通知类型的唯一ID
     * @param binder 视图绑定器实例
     */
    @Suppress("UNCHECKED_CAST")
    fun <T : Any> registerBinder(type: Int, binder: INotificationBinder<T>) {
        binders[type] = binder as INotificationBinder<Any>
    }

    /**
     * 显示一条通知。
     * @param type 通知的类型ID
     * @param model 与该通知绑定的数据模型
     */
    fun <T : Any> show(type: Int, model: T): String? {
        val binder = binders[type] ?: return null
        val activity = currentActivity?.get() ?: return null

        // --- 视图复用/更新逻辑 ---
        val existingNotification = activeNotifications.find { it.type == type }
        if (existingNotification != null) {
            activeViews[existingNotification.id]?.let { view ->
                // 找到了已存在的视图，直接复用并更新它
                val timeout = binder.getTimeoutMillis()
                val newExpirationTime = if (timeout > 0) System.currentTimeMillis() + timeout else null

                // 更新数据模型
                val updatedData = existingNotification.copy(model = model, expirationTime = newExpirationTime)
                val index = activeNotifications.indexOf(existingNotification)
                if (index != -1) {
                    activeNotifications[index] = updatedData
                }

                // 重新绑定视图以更新内容
                binder.bind(view, model, existingNotification.id, currentDarkMode) { hide(existingNotification.id) }

                // 重置超时任务
                cancelTimeout(existingNotification.id)
                scheduleTimeout(existingNotification.id, timeout)
                
                // 因为是更新，所以不需要重绘所有视图或播放入场动画
                return existingNotification.id
            }
        }
        
        // --- 如果没有可复用的视图，则走创建新视图的逻辑 ---
        val id = UUID.randomUUID().toString()
        val priority = binder.getPriority()
        val timeout = binder.getTimeoutMillis()
        val expirationTime = if (timeout > 0) System.currentTimeMillis() + timeout else null
        val notificationData = NotificationData(id, type, model, priority, expirationTime)
        
        val container = getOrCreateContainerFor(activity)
        val inflater = LayoutInflater.from(activity)
        val view = inflater.inflate(binder.getLayoutId(), container, false)
        view.visibility = View.INVISIBLE

        activeViews[id] = view
        activeNotifications.add(notificationData)
        binder.bind(view, model, id, currentDarkMode) { hide(id) }

        activeNotifications.sortByDescending { it.priority }
        redrawViews(container)

        binder.onShowAnimation(view)

        scheduleTimeout(id, timeout)

        return id
    }

    /**
     * 隐藏当前正在显示的通知。
     */
    fun hide(id: String) {
        cancelTimeout(id)

        val view = activeViews.remove(id) ?: return
        val notificationData = activeNotifications.find { it.id == id } ?: return
        val binder = binders[notificationData.type] ?: return

        binder.onHideAnimation(view) {
            (view.parent as? ViewGroup)?.removeView(view)
            activeNotifications.remove(notificationData)
            binder.onDismissed(id)
        }
    }

    // --- 换肤事件监听 ---

    /**
     * 监听换肤事件
     * 当收到 ThemeChangeEvent 时，重新绑定所有活跃的通知
     * 添加防抖机制，避免同一状态重复调用
     */
    fun onEventMainThread(event: ThemeChangeEvent) {
        // 更新状态
        if(currentDarkMode == event.isDarkMode) return
        currentDarkMode = event.isDarkMode
        // 重新绑定所有活跃的通知
        refreshAllActiveNotifications()
    }

    /**
     * 刷新所有活跃通知的视图绑定
     * 在换肤时调用，重新应用当前主题
     */
    private fun refreshAllActiveNotifications() {
        if (activeNotifications.isEmpty()) return

        activeNotifications.forEach { data ->
            val view = activeViews[data.id]
            val binder = binders[data.type]
            if (view != null && binder != null) {
                // 重新绑定视图以应用新主题
                binder.bind(view, data.model, data.id, currentDarkMode) { hide(data.id) }
            }
        }
    }

    /**
     * 获取当前深色模式状态
     * @return true 如果当前是深色模式，false 否则
     */
    fun getCurrentDarkMode(): Boolean {
        return currentDarkMode
    }

    /**
     * 设置当前深色模式状态（用于初始化）
     * @param isDarkMode 是否是深色模式
     */
    fun setCurrentDarkMode(isDarkMode: Boolean) {
        currentDarkMode = isDarkMode
    }

    // --- 私有辅助方法 ---

    private fun getOrCreateContainerFor(activity: Activity): FrameLayout {
        return containers.getOrPut(activity) {
            val decorView = activity.window.decorView as FrameLayout
            val newContainer = FrameLayout(activity)
            val params = FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.WRAP_CONTENT
            ).apply {
                topMargin = getStatusBarHeight()
            }
            decorView.addView(newContainer, params)
            newContainer
        }
    }
    
    private fun redrawViews(container: FrameLayout) {
        container.removeAllViews()
        activeNotifications.sortedBy { it.priority }.forEach { data ->
            activeViews[data.id]?.let { view ->
                (view.parent as? ViewGroup)?.removeView(view)
                container.addView(view)
            }
        }
    }

    private fun getStatusBarHeight(): Int {
        return BarUtils.getStatusBarHeight()
    }

    private fun scheduleTimeout(id: String, delay: Long) {
        if (delay <= 0) return
        val runnable = Runnable { hide(id) }
        timeoutRunnables[id] = runnable
        timeoutHandler.postDelayed(runnable, delay)
    }

    private fun cancelTimeout(id: String) {
        timeoutRunnables.remove(id)?.let {
            timeoutHandler.removeCallbacks(it)
        }
    }

    private fun cancelAllTimeouts() {
        if (timeoutRunnables.isEmpty()) return
        timeoutRunnables.values.forEach { timeoutHandler.removeCallbacks(it) }
        timeoutRunnables.clear()
    }

    // --- ActivityLifecycleCallbacks 实现 ---

    override fun onActivityResumed(activity: Activity) {
        currentActivity = WeakReference(activity)
        if (activeNotifications.isEmpty()) return
        
        // 1. 清理在后台过期的通知
        val now = System.currentTimeMillis()
        val iterator = activeNotifications.iterator()
        while(iterator.hasNext()) {
            val data = iterator.next()
            if (data.expirationTime != null && now >= data.expirationTime) {
                iterator.remove() // 从数据源中移除
                activeViews.remove(data.id) // 从视图缓存中移除
                binders[data.type]?.onDismissed(data.id) // 通知 Binder，虽然此时没有计时器需要清理
            }
        }

        // 2. 为剩余的有效通知创建视图并重新安排计时器
        val container = getOrCreateContainerFor(activity)
        val newViews = mutableMapOf<String, View>()
        activeNotifications.forEach { data ->
            val binder = binders[data.type] ?: return@forEach
            val inflater = LayoutInflater.from(activity)
            val view = inflater.inflate(binder.getLayoutId(), container, false)
            newViews[data.id] = view
            binder.bind(view, data.model, data.id, currentDarkMode) { hide(data.id) }

            // 重新为每个未过期的通知安排剩余时间的超时任务
            data.expirationTime?.let { expTime ->
                val remainingTime = expTime - now
                if (remainingTime > 0) {
                    scheduleTimeout(data.id, remainingTime)
                }
            }
        }
        activeViews.clear()
        activeViews.putAll(newViews)
        redrawViews(container)
    }

    override fun onActivityPaused(activity: Activity) {
        // 当一个 Activity 暂停时，精确地移除只属于它的容器。
        containers.remove(activity)?.let { container ->
            (container.parent as? ViewGroup)?.removeView(container)
        }

        // 同时，取消所有正在运行的计时器，因为视图已不再可见。
        cancelAllTimeouts()
    }
    
    override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {}
    override fun onActivityStarted(activity: Activity) {}
    override fun onActivityStopped(activity: Activity) {}
    override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {}
    override fun onActivityDestroyed(activity: Activity) {
        // 确保 Activity 销毁时也清理容器，作为双重保障。
        containers.remove(activity)
    }
} 