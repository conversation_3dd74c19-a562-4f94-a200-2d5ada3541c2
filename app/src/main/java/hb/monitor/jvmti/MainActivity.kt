package hb.monitor.jvmti

import android.content.Intent
import android.content.Intent.ACTION_VIEW
import android.net.Uri
import androidx.appcompat.app.AppCompatActivity
import android.os.Bundle
import hb.monitor.demo.Demo1Activity
import hb.monitor.demo.GiftPanelActivity
import hb.monitor.demo.GradientTextDemoActivity
import hb.monitor.demo.HeartAnimationDemoActivity
import hb.monitor.demo.HeartFlyDemoActivity
import hb.monitor.demo.HeartViewDemoActivity
import hb.monitor.demo.StartUpActivity
import hb.monitor.demo.ViewPagerTestActivity
import hb.monitor.familytag.Demo2Activity
import hb.monitor.jvmti.databinding.ActivityMainBinding
import hb.monitor.jvmti.dialogfragment.Test1DialogFragment
import hb.monitor.demo.GalleryDemoActivity
import hb.monitor.jvmti.ChatInviteModel
import hb.monitor.jvmti.InAppNotificationManager
import hb.monitor.jvmti.ThemeChangeEvent
import hb.xstatic.tools.XEventBus
import java.util.UUID

class MainActivity : AppCompatActivity() {
    private val mBinding by lazy {
        ActivityMainBinding.inflate(layoutInflater)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(mBinding.root)

        initClickListener()
    }

    private fun initClickListener() {
        mBinding.btn1.setOnClickListener {
//            Demo1Activity.start(this)
            for (i in 0..5) {
//                val intent = packageManager.getLaunchIntentForPackage("com.xingjiabi.shengsheng")
                val intent = Intent()
                intent?.let {
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_RESET_TASK_IF_NEEDED)
                    intent.setAction(Intent.ACTION_MAIN)
                    intent.addCategory(Intent.CATEGORY_LAUNCHER)
                    intent.setClassName("com.xingjiabi.shengsheng","com.xingjiabi.shengsheng.app.SplashActivity")
//                    intent.setFlags(0x10200000)
                    startActivity(intent)
                }

//                intent.setData(Uri.parse("ushengsheng.xjb://xingjiabi/"))
            }
        }


        mBinding.btn2.setOnClickListener {
            Demo1Activity.start(this)
        }

        mBinding.btn3.setOnClickListener {
            StartUpActivity.start(this)
        }

        mBinding.btn4.setOnClickListener {
            //
//            GiftPanelActivity.start(this)
//            Test1DialogFragment().show(supportFragmentManager.beginTransaction(),"")
            ViewPagerTestActivity.start(this)
        }
        
        mBinding.btn5.setOnClickListener {
            // 启动爱心动画演示
            startActivity(Intent(this, HeartAnimationDemoActivity::class.java))
        }
        
        mBinding.btn6.setOnClickListener {
            // 启动爱心呼吸效果演示
            startActivity(Intent(this, HeartViewDemoActivity::class.java))
        }
        
        mBinding.btn7.setOnClickListener {
            // 启动爱心组合测试
            startActivity(Intent(this, HeartTestActivity::class.java))
        }
        
        mBinding.btn8.setOnClickListener {
            // 启动爱心飘飞特效演示
            startActivity(Intent(this, HeartFlyDemoActivity::class.java))
        }
        
        mBinding.btn9.setOnClickListener {
            // 启动渐变文本效果演示
            startActivity(Intent(this, GradientTextDemoActivity::class.java))
        }

        mBinding.btnGalleryDemo.setOnClickListener {
            GalleryDemoActivity.start(this)
        }

        mBinding.btnShowNotification.setOnClickListener {
            showInAppNotification()
        }

        mBinding.btnDarkMode.setOnClickListener {
            switchToDarkMode()
        }

        mBinding.btnLightMode.setOnClickListener {
            switchToLightMode()
        }

        mBinding.btnPkDemo.setOnClickListener {
            PKDemoActivity.start(this)
        }
    }

    private fun showInAppNotification() {
        val model = ChatInviteModel(
            title = "你预约的畅聊圈节目开始啦～快去围观！${UUID.randomUUID()}",
            buttonText = "去围观",
            imageResId = R.mipmap.ic_launcher // Replace with your actual image resource
        )
        // 使用 ID=1 来显示我们注册的 ChatInviteBinder
        InAppNotificationManager.show(1, model)
    }

    /**
     * 切换到深色模式
     */
    private fun switchToDarkMode() {
        // 发送深色模式事件
        XEventBus.post(ThemeChangeEvent(isDarkMode = true, themeName = "Dark"))
    }

    /**
     * 切换到浅色模式
     */
    private fun switchToLightMode() {
        // 发送浅色模式事件
        XEventBus.post(ThemeChangeEvent(isDarkMode = false, themeName = "Light"))
    }
}