package hb.monitor.jvmti

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import hb.monitor.jvmti.data.*
import hb.monitor.jvmti.databinding.ActivityPkDemoBinding
import hb.monitor.jvmti.viewmodel.PKViewModel
import hb.monitor.jvmti.widget.PKViewManager
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.launch

class PKDemoActivity : AppCompatActivity(), PKViewManager.PKStatusListener {

    private val binding by lazy {
        ActivityPkDemoBinding.inflate(layoutInflater)
    }

    // PK视图管理器，实现按需加载
    private lateinit var pkViewManager: PKViewManager

    // ViewModel
    private val pkViewModel: PKViewModel by viewModels()

    // 分数管理
    private var blueTeamScore = 0
    private var redTeamScore = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)

        // 初始化PK视图管理器
        pkViewManager = PKViewManager(this, binding.pkContainer)
        pkViewManager.setPKStatusListener(this)

        initListener()

        // 默认显示组队PK
        binding.rbTeamPk.isChecked = true
        pkViewManager.switchToPKMode(PKViewManager.PKMode.TEAM_PK)

        // 配置默认设置
        setupDefaultConfigurations()

        // 观察ViewModel状态
        observeViewModelStates()

        // 初始化测试数据
        initTestData()

        // 初始化调试按钮显示状态
        binding.llTestButtons.visibility = android.view.View.VISIBLE
        binding.llScoreTestButtons.visibility = android.view.View.GONE
    }

    private fun setupDefaultConfigurations() {
        // 可以根据不同场景设置不同的最大人数
        // 这些配置会在视图创建时应用
    }

    private fun initListener() {
        // 设置测试按钮监听器
        setupTestButtonListeners()
    }

    private fun setupTestButtonListeners() {
        binding.btnExtendEvent.setOnClickListener {
            // 延长活动逻辑
            // 这里可以添加延长PK时间的逻辑
        }

        binding.btnTestUpdate.setOnClickListener {
            // 通过ViewModel模拟单次更新
            pkViewModel.simulateDataUpdate()
        }

        binding.btnBatchUpdate.setOnClickListener {
            // 通过ViewModel模拟批量更新
            pkViewModel.simulateDataUpdate()
        }

        // 分数测试按钮
        binding.btnBlueScore.setOnClickListener {
            // 蓝方加1分
            simulateScoreChange(true)
        }

        binding.btnRedScore.setOnClickListener {
            // 红方加1分
            simulateScoreChange(false)
        }

        binding.btnStatusChange.setOnClickListener {
            // 状态切换
            simulatePKStatusChange()
        }

        binding.btnModeSwitch.setOnClickListener {
            // 模式切换
            simulateModeSwitch()
        }

        // RadioButton监听器
        binding.rgPkModes.setOnCheckedChangeListener { _, checkedId ->
            when (checkedId) {
                R.id.rb_team_pk -> {
                    // 切换到组队PK
                    switchToTeamPKMode()
                }
                R.id.rb_multiplayer_pk -> {
                    // 切换到多人PK
                    switchToMultiPlayerPKMode()
                }
            }
        }
    }

    /**
     * 更新进度条显示
     */
    private fun updateProgressBar() {
        val currentView = pkViewManager.getCurrentPKView()
        if (currentView is hb.monitor.jvmti.widget.TeamPKView) {
            // 获取当前视图的进度条并更新
            val progressBar = currentView.getProgressBar()
            progressBar.updateProgress(blueTeamScore, redTeamScore)
        }
    }

    // 实现PKStatusListener接口
    override fun onPKStarted() {
        binding.tvPkStatus.visibility = android.view.View.VISIBLE
    }

    override fun onPKEnded() {
        binding.tvPkStatus.visibility = android.view.View.VISIBLE // 保持状态显示可见
    }

    override fun onDestroy() {
        super.onDestroy()
        // 清理资源，释放内存
        pkViewManager.cleanup()
    }

    /**
     * 观察ViewModel状态
     */
    private fun observeViewModelStates() {
        // 观察PK数据变化
        lifecycleScope.launch {
            pkViewModel.pkData.collect { pkData ->
                pkData?.let { data ->
                    updateUIWithPKData(data)
                }
            }
        }

        // 观察PK状态变化
        lifecycleScope.launch {
            pkViewModel.isPKStarted.collect { isStarted ->
                updatePKStatus(isStarted)
            }
        }

        // 观察倒计时变化
        lifecycleScope.launch {
            pkViewModel.formattedCountdown.collect { countdown ->
                updateCountdownDisplay(countdown)
            }
        }

        // 观察组队PK分数变化
        lifecycleScope.launch {
            pkViewModel.blueTeamTotalScore.collect { score ->
                updateBlueTeamScore(score)
            }
        }

        lifecycleScope.launch {
            pkViewModel.redTeamTotalScore.collect { score ->
                updateRedTeamScore(score)
            }
        }

        // 观察PK类型变化
        lifecycleScope.launch {
            pkViewModel.isTeamPK.collect { isTeamPK ->
                updatePKTypeUI(isTeamPK)
            }
        }

        // 观察状态描述变化
        lifecycleScope.launch {
            pkViewModel.statusDescription.collect { description ->
                updateStatusDescription(description)
            }
        }
    }

    /**
     * 根据PK数据更新UI
     */
    private fun updateUIWithPKData(pkData: PKResponse) {
        // 根据PK类型切换视图
        updatePKTypeUI(pkData.isTeamPK())

        // 绑定数据到当前PK视图
        val currentView = pkViewManager.getCurrentPKView()
        currentView?.bindPKData(pkData)

        // 设置ViewModel引用到PK视图，让视图可以更新状态
        if (currentView is hb.monitor.jvmti.widget.TeamPKView) {
            currentView.setPKViewModel(pkViewModel)
        } else if (currentView is hb.monitor.jvmti.widget.MultiplayerPKView) {
            currentView.setPKViewModel(pkViewModel)
        }

        // 更新状态显示
        updateStatusDescription(pkViewModel.statusDescription.value)

        // 根据具体状态更新UI
        when (pkData.status) {
            PKStatus.NOT_STARTED -> {
                // 未开始状态
                binding.tvPkStatus.setTextColor(android.graphics.Color.GRAY)
            }
            PKStatus.PREPARING -> {
                // 准备中状态
                binding.tvPkStatus.setTextColor(android.graphics.Color.YELLOW)
            }
            PKStatus.IN_PROGRESS -> {
                // 进行中状态
                binding.tvPkStatus.setTextColor(android.graphics.Color.GREEN)
            }
            PKStatus.ENDED -> {
                // 已结束状态
                binding.tvPkStatus.setTextColor(android.graphics.Color.RED)
            }
        }
    }

    /**
     * 初始化测试数据
     */
    private fun initTestData() {
        // 创建测试的组队PK数据
        val testTeamPKData = PKResponse(
            status = PKStatus.NOT_STARTED,
            remind_time = 300, // 5分钟
            round_id = "test_round_001",
            rule = "测试规则说明",
            hash = "test_hash_${System.currentTimeMillis()}",
            team_pk = TeamPKData(
                blue_team = TeamData(
                    total_count = 0,
                    members = listOf(
                        PKMember("avatar1", "user001", 0, 0),
                        PKMember("avatar2", "user002", 0, 0)
                    )
                ),
                red_team = TeamData(
                    total_count = 0,
                    members = listOf(
                        PKMember("avatar3", "user003", 0, 0),
                        PKMember("avatar4", "user004", 0, 0)
                    )
                )
            ),
            multi_player_pk = null
        )

        // 更新到ViewModel
        pkViewModel.updatePKData(testTeamPKData)
    }

    /**
     * 更新PK状态
     */
    private fun updatePKStatus(isStarted: Boolean) {
        // 更新调试按钮显示状态
        updateDebugButtonsVisibility(isStarted)
    }

    /**
     * 更新调试按钮的显示状态
     */
    private fun updateDebugButtonsVisibility(isPKStarted: Boolean) {
        // 状态切换和模式切换按钮始终显示
        binding.llTestButtons.visibility = android.view.View.VISIBLE

        // 分数测试按钮只在PK进行中时显示
        if (isPKStarted) {
            binding.llScoreTestButtons.visibility = android.view.View.VISIBLE
        } else {
            binding.llScoreTestButtons.visibility = android.view.View.GONE
        }
    }

    /**
     * 更新倒计时显示
     */
    private fun updateCountdownDisplay(countdown: String) {
        val statusText = "倒计时: $countdown"
        binding.tvPkStatus.text = statusText
    }

    /**
     * 更新蓝方分数
     */
    private fun updateBlueTeamScore(score: Int) {
        blueTeamScore = score
        updateProgressBar()
    }

    /**
     * 更新红方分数
     */
    private fun updateRedTeamScore(score: Int) {
        redTeamScore = score
        updateProgressBar()
    }



    /**
     * 更新PK类型UI
     */
    private fun updatePKTypeUI(isTeamPK: Boolean) {
        // 临时移除监听器，避免循环触发
        binding.rgPkModes.setOnCheckedChangeListener(null)

        if (isTeamPK) {
            binding.rbTeamPk.isChecked = true
            pkViewManager.switchToPKMode(PKViewManager.PKMode.TEAM_PK)
        } else {
            binding.rbMultiplayerPk.isChecked = true
            pkViewManager.switchToPKMode(PKViewManager.PKMode.MULTIPLAYER_PK)
        }

        // 重新设置监听器
        binding.rgPkModes.setOnCheckedChangeListener { _, checkedId ->
            when (checkedId) {
                R.id.rb_team_pk -> {
                    // 切换到组队PK
                    switchToTeamPKMode()
                }
                R.id.rb_multiplayer_pk -> {
                    // 切换到多人PK
                    switchToMultiPlayerPKMode()
                }
            }
        }
    }

    /**
     * 更新状态描述
     */
    private fun updateStatusDescription(description: String) {
        val currentData = pkViewModel.getCurrentPKData()
        val statusText = if (currentData != null) {
            val pkType = if (currentData.isTeamPK()) "组队PK" else "多人PK"
            "模式: $pkType | 状态: $description | 场次: ${currentData.round_id}"
        } else {
            "状态: $description"
        }
        binding.tvPkStatus.text = statusText
    }

    /**
     * 模拟PK状态变化（测试用）
     */
    private fun simulatePKStatusChange() {
        val currentData = pkViewModel.getCurrentPKData()
        if (currentData != null) {
            val newStatus = when (currentData.status) {
                PKStatus.NOT_STARTED -> PKStatus.PREPARING
                PKStatus.PREPARING -> PKStatus.IN_PROGRESS
                PKStatus.IN_PROGRESS -> PKStatus.ENDED
                PKStatus.ENDED -> PKStatus.NOT_STARTED
                else -> PKStatus.NOT_STARTED
            }

            val updatedData = currentData.copy(
                status = newStatus,
                hash = "${currentData.hash}_${System.currentTimeMillis()}"
            )
            pkViewModel.updatePKData(updatedData)
        }
    }

    /**
     * 模拟分数变化（测试用）
     */
    private fun simulateScoreChange(isBlueTeam: Boolean) {
        val currentData = pkViewModel.getCurrentPKData()
        if (currentData?.team_pk != null) {
            val teamPK = currentData.team_pk
            val updatedTeamPK = if (isBlueTeam) {
                teamPK.copy(
                    blue_team = teamPK.blue_team.copy(
                        total_count = teamPK.blue_team.total_count + 1
                    )
                )
            } else {
                teamPK.copy(
                    red_team = teamPK.red_team.copy(
                        total_count = teamPK.red_team.total_count + 1
                    )
                )
            }

            val updatedData = currentData.copy(
                team_pk = updatedTeamPK,
                hash = "${currentData.hash}_${System.currentTimeMillis()}"
            )
            pkViewModel.updatePKData(updatedData)
        }
    }

    /**
     * 模拟模式切换（测试用）
     */
    private fun simulateModeSwitch() {
        val currentData = pkViewModel.getCurrentPKData()
        if (currentData != null) {
            // 如果当前是组队PK，切换到多人PK
            // 如果当前是多人PK，切换到组队PK
            val isCurrentlyTeamPK = currentData.isTeamPK()

            val updatedData = if (isCurrentlyTeamPK) {
                // 切换到多人PK
                PKResponse(
                    status = currentData.status,
                    remind_time = currentData.remind_time,
                    round_id = currentData.round_id,
                    rule = currentData.rule,
                    hash = "${currentData.hash}_mode_switch_${System.currentTimeMillis()}",
                    team_pk = null,
                    multi_player_pk = MultiPlayerPKData(
                        members = listOf(
                            PKMember("avatar1", "user001", 0, 10),
                            PKMember("avatar2", "user002", 0, 8),
                            PKMember("avatar3", "user003", 0, 6),
                            PKMember("avatar4", "user004", 0, 4)
                        )
                    )
                )
            } else {
                // 切换到组队PK
                PKResponse(
                    status = currentData.status,
                    remind_time = currentData.remind_time,
                    round_id = currentData.round_id,
                    rule = currentData.rule,
                    hash = "${currentData.hash}_mode_switch_${System.currentTimeMillis()}",
                    team_pk = TeamPKData(
                        blue_team = TeamData(
                            total_count = 10,
                            members = listOf(
                                PKMember("avatar1", "user001", 0, 6),
                                PKMember("avatar2", "user002", 0, 4)
                            )
                        ),
                        red_team = TeamData(
                            total_count = 8,
                            members = listOf(
                                PKMember("avatar3", "user003", 0, 5),
                                PKMember("avatar4", "user004", 0, 3)
                            )
                        )
                    ),
                    multi_player_pk = null
                )
            }

            // 更新数据
            pkViewModel.updatePKData(updatedData)
        }
    }

    /**
     * 切换到组队PK模式
     */
    private fun switchToTeamPKMode() {
        val currentData = pkViewModel.getCurrentPKData()
        if (currentData != null && !currentData.isTeamPK()) {
            // 创建组队PK数据
            val teamPKData = PKResponse(
                status = currentData.status,
                remind_time = currentData.remind_time,
                round_id = currentData.round_id,
                rule = currentData.rule,
                hash = "${currentData.hash}_switch_team_${System.currentTimeMillis()}",
                team_pk = TeamPKData(
                    blue_team = TeamData(
                        total_count = 0,
                        members = listOf(
                            PKMember("avatar1", "user001", 0, 0),
                            PKMember("avatar2", "user002", 0, 0)
                        )
                    ),
                    red_team = TeamData(
                        total_count = 0,
                        members = listOf(
                            PKMember("avatar3", "user003", 0, 0),
                            PKMember("avatar4", "user004", 0, 0)
                        )
                    )
                ),
                multi_player_pk = null
            )
            pkViewModel.updatePKData(teamPKData)
        }
    }

    /**
     * 切换到多人PK模式
     */
    private fun switchToMultiPlayerPKMode() {
        val currentData = pkViewModel.getCurrentPKData()
        if (currentData != null && !currentData.isMultiPlayerPK()) {
            // 创建多人PK数据
            val multiPlayerPKData = PKResponse(
                status = currentData.status,
                remind_time = currentData.remind_time,
                round_id = currentData.round_id,
                rule = currentData.rule,
                hash = "${currentData.hash}_switch_multi_${System.currentTimeMillis()}",
                team_pk = null,
                multi_player_pk = MultiPlayerPKData(
                    members = listOf(
                        PKMember("avatar1", "user001", 0, 0),
                        PKMember("avatar2", "user002", 0, 0),
                        PKMember("avatar3", "user003", 0, 0),
                        PKMember("avatar4", "user004", 0, 0)
                    )
                )
            )
            pkViewModel.updatePKData(multiPlayerPKData)
        }
    }

    companion object {
        fun start(context: Context) {
            context.startActivity(Intent(context, PKDemoActivity::class.java))
        }
    }
}