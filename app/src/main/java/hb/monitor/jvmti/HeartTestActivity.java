package hb.monitor.jvmti;

import android.content.Context;
import android.os.Build;
import android.os.Bundle;
import android.os.VibrationEffect;
import android.os.Vibrator;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import hb.monitor.weiget.HeartAnimationView;
import hb.monitor.weiget.HeartView;
import hb.utils.SizeUtils;
import kotlin.Suppress;

public class HeartTestActivity extends AppCompatActivity implements HeartView.HeartBeatListener {

    private HeartAnimationView heartAnimationView;
    private HeartView heartView;
    
    // 每次心跳产生的爱心数量
    private static final int HEARTS_PER_BEAT = 1;
    private Vibrator vibrator;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_heart_test);
        vibrator = (Vibrator) getSystemService(Context.VIBRATOR_SERVICE);

        // 获取视图引用
        heartAnimationView = findViewById(R.id.heart_animation_view);
        heartView = findViewById(R.id.heart_view);
        
        // 设置心跳监听器 - 适配Kotlin接口
        heartView.setHeartBeatListener(this);
        
        // 确保HeartAnimationView已启动
        heartAnimationView.start();
    }

    @Override
    public void onHeartBeat(float x, float y) {

        // 在HeartView的位置添加爱心效果
        if (heartAnimationView != null) {
            vibrateDevice();
            // 需要将HeartView相对于父视图的坐标转换为HeartAnimationView中的坐标
            
            // 获取HeartView在屏幕中的位置
            int[] heartViewLocation = new int[2];
            heartView.getLocationOnScreen(heartViewLocation);
            
            // 获取HeartAnimationView在屏幕中的位置
            int[] animViewLocation = new int[2];
            heartAnimationView.getLocationOnScreen(animViewLocation);
            
            // 计算相对坐标
            float heartX = heartViewLocation[0] + heartView.getWidth() / 2 - animViewLocation[0];
            float heartY = heartViewLocation[1] + heartView.getHeight() / 2 - animViewLocation[1];
            
            // 在HeartView的位置添加HEARTS_PER_BEAT个爱心
            heartAnimationView.addHearts(heartX, heartY, HEARTS_PER_BEAT, 32f);
        }
    }

    @Override
    protected void onDestroy() {
        // 停止HeartAnimationView动画
        if (heartAnimationView != null) {
            heartAnimationView.stop();
        }
        super.onDestroy();
    }

    // 震动设备的方法
    private void vibrateDevice() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            // 新API使用VibrationEffect
            vibrator.vibrate(VibrationEffect.createOneShot(50, VibrationEffect.DEFAULT_AMPLITUDE));
        } else {
            // 旧版本的震动方法
            vibrator.vibrate(50);
        }
    }
} 