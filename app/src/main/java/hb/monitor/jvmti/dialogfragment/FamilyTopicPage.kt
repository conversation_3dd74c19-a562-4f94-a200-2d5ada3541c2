package hb.monitor.jvmti.dialogfragment

/**
 * 家族话题页面进栈管理
 *
 * @param name 用于回退栈定位、以及返回参数监听
 *
 * <AUTHOR>
 * @date 2024-10-29
 */
sealed class FamilyTopicPage(name:String) {

    /**
     * 主页面
     */
    class MainPage : FamilyTopicPage(name = "main")

    /**
     * 发布话题
     */
    class PublishPage:FamilyTopicPage(name = "publish")


    /**
     * AI关键字输入页面
     */
    class AIKeywordInputPage:FamilyTopicPage(name = "ai_keyword_input")

    /**
     * AI生成结果页
     */
    class AIResultPage:FamilyTopicPage(name = "ai_result")

    /**
     * 话题详情页
     */
    class TopicDetailPage:FamilyTopicPage(name = "topic_detail")

    /**
     * 话题消息-通知消息页
     */
    class TopicNotificationPage:FamilyTopicPage(name = "topic_notification")
}