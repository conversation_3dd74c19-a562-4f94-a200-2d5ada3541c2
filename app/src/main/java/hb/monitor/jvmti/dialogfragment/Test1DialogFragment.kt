package hb.monitor.jvmti.dialogfragment

import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import hb.monitor.jvmti.R
import hb.utils.SizeUtils

/**
 *
 *
 * <AUTHOR>
 * @date 2024-10-28
 */
class Test1DialogFragment : DialogFragment(R.layout.dialog_fragment), IFamilyTopicDialog {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        childFragmentManager.beginTransaction()
            .replace(R.id.content, Test2Fragment.newInstance("1"))
            .commit()
    }

    override fun onStart() {
        super.onStart()
        val window = dialog?.window
        // 设置宽度为铺满
        window?.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, SizeUtils.dp2px(300f))
        // 设置布局内容显示在底部
        window?.setGravity(Gravity.BOTTOM)
    }
}