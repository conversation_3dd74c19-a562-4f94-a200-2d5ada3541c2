package hb.monitor.jvmti.dialogfragment

import android.os.Bundle
import android.util.Log
import android.view.View
import androidx.fragment.app.Fragment
import hb.monitor.jvmti.R
import hb.monitor.jvmti.databinding.FragmentBinding

/**
 *
 * <AUTHOR>
 * @date 2024-10-28
 */
class Test2Fragment : Fragment(R.layout.fragment) {

    companion object {
        fun newInstance(page: String): Test2Fragment {
            val args = Bundle()
            args.putString("page", page)
            val fragment = Test2Fragment()
            fragment.arguments = args
            return fragment
        }
    }

    var count = 0

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val mBinding = FragmentBinding.bind(requireView())
        val page = arguments?.getString("page")?:"-1"
        Log.d("kyluzoi","当前页数:$page")

        mBinding.tvPage.text = "当前页数:$page"
        mBinding.tvPage.setOnClickListener {
            Log.d("kyluzoi","当前页数:$page --- 下一页")
            parentFragmentManager.beginTransaction()
                .add(R.id.content, Test2Fragment.newInstance((page.toInt() + 1).toString()))
                .hide(this)
                .addToBackStack(null) // 将操作添加到返回栈
                .commit()
        }

        mBinding.tvCount.text = "count: $count"
        mBinding.tvCount.setOnClickListener {
            count++
            mBinding.tvCount.text = "count: $count"
        }

        mBinding.tvBack.setOnClickListener {
            Log.d("kyluzoi","当前页数:$page --- 点击返回")
            parentFragmentManager.popBackStack()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        Log.d("kyluzoi","销毁View")
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d("kyluzoi","onDestroy11")
    }
}