package hb.monitor.jvmti.dialogfragment.viewmodel

import androidx.lifecycle.ViewModel
import hb.monitor.jvmti.dialogfragment.FamilyTopicPage
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow

/**
 * 家族话题页面管理 ViewModel
 *
 * <AUTHOR>
 * @date 2024-10-28
 */
class FamilyTopicPageViewModel : ViewModel() {
    private val _pageFlow: MutableSharedFlow<FamilyTopicPage> =
        MutableSharedFlow(0, extraBufferCapacity = 1, onBufferOverflow = BufferOverflow.DROP_OLDEST)
    val pageFlow: SharedFlow<FamilyTopicPage> = _pageFlow
}