package hb.monitor.jvmti.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import hb.monitor.jvmti.data.*
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

/**
 * PK数据ViewModel
 * 使用Flow管理PK数据状态
 */
class PKViewModel : ViewModel() {
    
    // 私有可变状态
    private val _pkData = MutableStateFlow<PKResponse?>(null)
    private val _countdown = MutableStateFlow(0)
    
    // 公开只读状态
    val pkData: StateFlow<PKResponse?> = _pkData.asStateFlow()
    val countdown: StateFlow<Int> = _countdown.asStateFlow()
    
    // 派生状态
    val pkStatus: StateFlow<Int> = pkData.map { it?.status ?: PKStatus.NOT_STARTED }
        .stateIn(viewModelScope, SharingStarted.WhileSubscribed(), PKStatus.NOT_STARTED)
    
    val isTeamPK: StateFlow<Boolean> = pkData.map { it?.isTeamPK() == true }
        .stateIn(viewModelScope, SharingStarted.WhileSubscribed(), false)
    
    val isMultiPlayerPK: StateFlow<Boolean> = pkData.map { it?.isMultiPlayerPK() == true }
        .stateIn(viewModelScope, SharingStarted.WhileSubscribed(), false)
    
    val isPKStarted: StateFlow<Boolean> = pkData.map { it?.isPKStarted() == true }
        .stateIn(viewModelScope, SharingStarted.WhileSubscribed(), false)
    
    val isPKPreparing: StateFlow<Boolean> = pkData.map { it?.isPKPreparing() == true }
        .stateIn(viewModelScope, SharingStarted.WhileSubscribed(), false)
    
    val isPKEnded: StateFlow<Boolean> = pkData.map { it?.isPKEnded() == true }
        .stateIn(viewModelScope, SharingStarted.WhileSubscribed(), false)
    
    // 组队PK相关状态
    val blueTeamTotalScore: StateFlow<Int> = pkData.map { it?.team_pk?.blue_team?.total_count ?: 0 }
        .stateIn(viewModelScope, SharingStarted.WhileSubscribed(), 0)

    val redTeamTotalScore: StateFlow<Int> = pkData.map { it?.team_pk?.red_team?.total_count ?: 0 }
        .stateIn(viewModelScope, SharingStarted.WhileSubscribed(), 0)
    
    // 格式化的倒计时文本
    val formattedCountdown: StateFlow<String> = countdown.map { seconds ->
        formatCountdown(seconds)
    }.stateIn(viewModelScope, SharingStarted.WhileSubscribed(), "00:00")
    
    // 状态描述
    val statusDescription: StateFlow<String> = pkStatus.map { status ->
        when (status) {
            PKStatus.NOT_STARTED -> "未开始"
            PKStatus.PREPARING -> "准备中"
            PKStatus.IN_PROGRESS -> "进行中"
            PKStatus.ENDED -> "已结束"
            else -> "未知状态"
        }
    }.stateIn(viewModelScope, SharingStarted.WhileSubscribed(), "未开始")
    
    /**
     * 更新PK数据
     */
    fun updatePKData(newData: PKResponse) {
        viewModelScope.launch {
            val currentData = _pkData.value
            
            // 检查幂等性
            if (currentData?.hash == newData.hash) {
                // 只更新倒计时
                if (currentData.remind_time != newData.remind_time) {
                    _countdown.value = newData.remind_time
                }
                return@launch
            }
            
            // 更新数据
            _pkData.value = newData
            _countdown.value = newData.remind_time
        }
    }
    
    /**
     * 更新倒计时
     */
    fun updateCountdown(seconds: Int) {
        _countdown.value = seconds
    }
    

    
    /**
     * 格式化倒计时显示
     */
    private fun formatCountdown(seconds: Int): String {
        if (seconds <= 0) return "00:00"
        
        val minutes = seconds / 60
        val remainingSeconds = seconds % 60
        return String.format("%02d:%02d", minutes, remainingSeconds)
    }
    
    /**
     * 获取当前PK数据（同步方法，用于兼容现有代码）
     */
    fun getCurrentPKData(): PKResponse? = _pkData.value
    
    /**
     * 获取当前状态（同步方法，用于兼容现有代码）
     */
    fun getCurrentStatus(): Int = _pkData.value?.status ?: PKStatus.NOT_STARTED
    
    /**
     * 模拟数据更新（用于测试）
     */
    fun simulateDataUpdate() {
        val currentData = _pkData.value
        if (currentData != null) {
            // 模拟分数变化
            val newData = if (currentData.isTeamPK()) {
                currentData.copy(
                    team_pk = currentData.team_pk?.copy(
                        blue_team = currentData.team_pk.blue_team.copy(
                            total_count = currentData.team_pk.blue_team.total_count + 1
                        )
                    ),
                    hash = "${currentData.hash}_${System.currentTimeMillis()}" // 更新hash
                )
            } else {
                currentData.copy(
                    hash = "${currentData.hash}_${System.currentTimeMillis()}" // 更新hash
                )
            }
            updatePKData(newData)
        }
    }
}
