# 应用内通知管理器 - 换肤功能使用说明

## 概述

本文档说明如何使用 `InAppNotificationManager` 的换肤功能，实现通知视图的主题动态切换。

## 功能特性

1. **EventBus 集成**：通过 EventBus 监听全局换肤事件
2. **防抖机制**：避免同一状态重复调用，防止性能问题
3. **状态缓存**：通过事件获取深色模式状态，而非系统资源
4. **内存安全**：自动管理 EventBus 注册/注销，防止内存泄漏

## 使用方法

### 1. 初始化（在 Application 中）

```kotlin
class MyApplication : Application() {
    override fun onCreate() {
        super.onCreate()
        
        // 初始化通知管理器
        InAppNotificationManager.init(this)
        
        // 注册 EventBus 监听换肤事件
        InAppNotificationManager.registerThemeChangeListener()
        
        // 注册通知类型
        InAppNotificationManager.registerBinder(1, ChatInviteBinder())
    }
}
```

### 2. 发送换肤事件

```kotlin
// 切换到深色模式
EventBus.getDefault().post(ThemeChangeEvent(isDarkMode = true, themeName = "Dark"))

// 切换到浅色模式
EventBus.getDefault().post(ThemeChangeEvent(isDarkMode = false, themeName = "Light"))
```

### 3. 实现 INotificationBinder

```kotlin
class ChatInviteBinder : INotificationBinder<ChatInviteModel> {
    
    override fun bind(view: View, model: ChatInviteModel, notificationId: String, isDarkMode: Boolean, hideAction: () -> Unit) {
        // 绑定数据
        val tvTitle: TextView = view.findViewById(R.id.tv_notification_title)
        tvTitle.text = model.title
        
        // 根据深色模式调整UI样式
        if (isDarkMode) {
            // 深色模式样式
            tvTitle.setTextColor(ContextCompat.getColor(view.context, R.color.text_color_dark))
            view.setBackgroundResource(R.drawable.notification_bg_dark)
        } else {
            // 浅色模式样式
            tvTitle.setTextColor(ContextCompat.getColor(view.context, R.color.text_color_light))
            view.setBackgroundResource(R.drawable.notification_bg_light)
        }
        
        // 设置点击事件
        view.setOnClickListener { hideAction() }
    }
}
```

## 事件类定义

```kotlin
data class ThemeChangeEvent(
    val isDarkMode: Boolean,
    val themeName: String? = null
)
```

## 防抖机制

系统内置 300ms 防抖延迟，避免短时间内重复的换肤事件导致性能问题：

- 如果状态没有变化，忽略事件
- 如果时间间隔小于 300ms，忽略事件
- 只有状态变化且时间间隔足够时，才会重新绑定视图

## API 说明

### InAppNotificationManager

- `registerThemeChangeListener()`: 注册 EventBus 监听
- `unregisterThemeChangeListener()`: 注销 EventBus 监听
- `getCurrentDarkMode()`: 获取当前深色模式状态
- `setCurrentDarkMode(Boolean)`: 设置当前深色模式状态

### INotificationBinder

- `bind(view, model, notificationId, isDarkMode, hideAction)`: 绑定视图，新增 `isDarkMode` 参数

## 注意事项

1. **内存泄漏**：EventBus 会在 Application 中自动注册，无需手动管理
2. **状态同步**：深色模式状态通过事件传递，确保状态一致性
3. **性能优化**：防抖机制避免频繁重绘，提升性能
4. **兼容性**：所有现有的 INotificationBinder 实现都需要更新 bind 方法签名

## Demo 使用

在 MainActivity 中提供了两个按钮用于测试：

- "切换到深色模式" 按钮
- "切换到浅色模式" 按钮
- "显示应用内通知" 按钮

测试步骤：
1. 点击 "显示应用内通知" 显示通知
2. 点击 "切换到深色模式" 观察通知样式变化
3. 点击 "切换到浅色模式" 观察通知样式变化
