package hb.monitor.jvmti

import android.animation.ObjectAnimator
import android.view.View
import androidx.annotation.LayoutRes
import hb.utils.Loger

/**
 * 通知视图绑定器接口
 * 任何想要通过 InAppNotificationManager 显示的通知类型都需要提供此接口的实现。
 * @param T 通知的具体数据模型 (Data Model)
 */
interface INotificationBinder<in T> {

    /**
     * @return 返回此通知使用的布局文件资源 ID
     */
    @LayoutRes
    fun getLayoutId(): Int

    /**
     * 将数据模型绑定到视图上。
     * 你可以在这个方法里设置点击事件、加载图片、设置文本等。
     * @param view 根据 getLayoutId() 创建的视图实例
     * @param model 需要绑定的数据
     * @param notificationId 当前这条通知的唯一 ID
     * @param isDarkMode 当前是否是深色模式
     * @param hideAction 当需要隐藏此通知时应调用的回调函数
     */
    fun bind(view: View, model: T, notificationId: String, isDarkMode: Boolean, hideAction: () -> Unit)

    /**
     * @return 返回通知的优先级，数字越大，位置越靠上。默认为 0。
     */
    fun getPriority(): Int = 0

    /**
     * @return 返回通知的显示时长（毫秒）。返回 0 或负数表示不自动隐藏。
     */
    fun getTimeoutMillis(): Long = 0

    /**
     * 定义通知显示的动画。
     * @param view 通知的视图
     */
    fun onShowAnimation(view: View) {
        // 默认实现：简单的从上方滑入
        // 使用 post 是为了确保 view 的高度已经计算完成
        view.post {
            // 在这里，view 的高度是已知的
            view.translationY = -view.height.toFloat()
            view.visibility = View.VISIBLE // 在动画开始前使其可见
            ObjectAnimator.ofFloat(view, "translationY", -view.height.toFloat(), 0f).apply {
                duration = 300
                start()
            }
        }
    }

    /**
     * 定义通知隐藏的动画。
     * @param view 通知的视图
     * @param onEndAction 动画结束时必须调用的回调，以便管理器进行清理
     */
    fun onHideAnimation(view: View, onEndAction: () -> Unit) {
        // 默认实现：简单的向上方滑出
        view.post {
            ObjectAnimator.ofFloat(view, "translationY", view.translationY, -view.height.toFloat()).apply {
                duration = 300
                addUpdateListener {
                    if (view.parent == null) {
                        // 如果视图在动画过程中被移除，取消动画
                        it.cancel()
                    }
                }
                addListener(object : android.animation.AnimatorListenerAdapter() {
                    override fun onAnimationEnd(animation: android.animation.Animator) {
                        onEndAction()
                    }
                })
                start()
            }
        }
    }
    
    /**
     * 当通知被销毁（无论是手动还是超时）时调用。
     * 可以在这里清理资源，例如取消定时器。
     * @param notificationId 被销毁的通知的唯一 ID
     */
    fun onDismissed(notificationId: String) {}
} 