<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <!-- 进度条容器 -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="16dp"
        android:background="@drawable/bg_progress_background"
        android:clipChildren="true"
        android:clipToPadding="true">

        <!-- 进度条内容 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            android:clipChildren="true"
            android:clipToPadding="true">

            <!-- 蓝方进度 -->
            <View
                android:id="@+id/viewBlueProgress"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="0.5"
                android:background="@drawable/bg_progress_blue" />

            <!-- 红方进度 -->
            <View
                android:id="@+id/viewRedProgress"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="0.5"
                android:background="@drawable/bg_progress_red" />

        </LinearLayout>

    </FrameLayout>

</merge>
