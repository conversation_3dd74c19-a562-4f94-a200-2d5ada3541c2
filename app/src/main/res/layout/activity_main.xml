<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MainActivity">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Hello World!"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <Button
        android:id="@+id/btn1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <Button
        android:id="@+id/btn2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        app:layout_constraintStart_toEndOf="@id/btn1"
        app:layout_constraintTop_toTopOf="parent" />

    <Button
        android:id="@+id/btn3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:text="启动器调试"
        app:layout_constraintStart_toEndOf="@id/btn2"
        app:layout_constraintTop_toTopOf="parent" />

    <Button
        android:id="@+id/btn4"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="测试1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/btn1" />

    <Button
        android:id="@+id/btn5"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="爱心动画演示"
        app:layout_constraintStart_toEndOf="@id/btn4"
        app:layout_constraintTop_toBottomOf="@id/btn1" />

    <Button
        android:id="@+id/btn6"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="爱心呼吸效果"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/btn4" />

    <Button
        android:id="@+id/btn7"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="爱心组合测试"
        app:layout_constraintStart_toEndOf="@id/btn6"
        app:layout_constraintTop_toBottomOf="@id/btn4" />

    <Button
        android:id="@+id/btn8"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="爱心飘飞特效"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/btn6" />

    <Button
        android:id="@+id/btn9"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="渐变文本效果"
        app:layout_constraintStart_toEndOf="@id/btn8"
        app:layout_constraintTop_toBottomOf="@id/btn6" />

    <Button
        android:id="@+id/btnGalleryDemo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="画廊联动Demo"
        app:layout_constraintStart_toEndOf="@id/btn9"
        app:layout_constraintTop_toBottomOf="@id/btn6"
        />

    <Button
        android:id="@+id/btn_show_notification"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="显示应用内通知"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/btn8"
        />

    <Button
        android:id="@+id/btn_dark_mode"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="切换到深色模式"
        app:layout_constraintStart_toEndOf="@id/btn_show_notification"
        app:layout_constraintTop_toBottomOf="@id/btn8"
        android:layout_marginStart="10dp"
        />

    <Button
        android:id="@+id/btn_light_mode"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="切换到浅色模式"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/btn_show_notification"
        android:layout_marginTop="10dp"
        />

    <Button
        android:id="@+id/btn_pk_demo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="PK Demo"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/btn_light_mode" />

</androidx.constraintlayout.widget.ConstraintLayout>