<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- 爱心飘飞视图容器 -->
    <hb.monitor.weiget.HeartFlyView
        android:id="@+id/heart_fly_view"
        android:layout_width="match_parent"
        android:layout_height="375dp"
        android:background="@color/TH_Blue100"
        android:layout_centerInParent="true"
        android:visibility="visible" />

    <!-- 标题 -->
    <TextView
        android:id="@+id/title_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="16dp"
        android:background="#80FFFFFF"
        android:padding="8dp"
        android:text="爱心飘飞特效演示"
        android:textSize="20sp"
        android:textStyle="bold" />

    <!-- 底部控制面板 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="20dp"
        android:background="#80FFFFFF"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="8dp">

        <!-- 按钮面板 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_emit_one"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="20dp"
                android:text="发射一个爱心" />

            <Button
                android:id="@+id/btn_emit_multiple"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="发射多个爱心" />
        </LinearLayout>
    </LinearLayout>

</RelativeLayout> 