<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#191919"
    tools:context="hb.monitor.demo.HeartAnimationDemoActivity">

    <!-- 模拟直播间信息区域 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/info_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#333333"
        android:padding="16dp"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/room_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="一颗葡萄树"
            android:textColor="#FFFFFF"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/user_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="1人已入驻"
            android:textColor="#AAAAAA"
            android:textSize="14sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/room_title" />
            
        <CheckBox
            android:id="@+id/collision_checkbox"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="底部碰撞"
            android:textColor="#FFFFFF"
            android:checked="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 爱心动画的容器 -->
    <hb.monitor.weiget.HeartAnimationView
        android:id="@+id/heart_animation_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/bottom_controls"
        app:layout_constraintTop_toBottomOf="@id/info_layout" />

    <!-- 底部控制栏 -->
    <LinearLayout
        android:id="@+id/bottom_controls"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:background="#333333"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        app:layout_constraintBottom_toBottomOf="parent">

        <TextView
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="1"
            android:background="@drawable/comment_box_bg"
            android:gravity="center_vertical"
            android:paddingStart="16dp"
            android:text="说点什么..."
            android:textColor="#999999"
            android:textSize="14sp" />

        <Space
            android:layout_width="16dp"
            android:layout_height="wrap_content" />

        <!-- 爱心按钮 -->
        <ImageView
            android:id="@+id/heart_button"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="发送爱心"
            android:padding="8dp"
            android:src="@drawable/heart_icon" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout> 