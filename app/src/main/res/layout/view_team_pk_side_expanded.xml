<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="194dp">

    <!-- RecyclerView -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvUsers"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="48dp" />

    <!-- 单一的添加按钮，通过代码动态调整位置 -->
    <hb.ximage.fresco.BaseDraweeView
        android:id="@+id/ivAddMember"
        android:layout_width="36dp"
        android:layout_height="36dp"
        android:clickable="true"
        android:focusable="true"
        android:visibility="gone"
        tools:visibility="visible"/>



</FrameLayout>