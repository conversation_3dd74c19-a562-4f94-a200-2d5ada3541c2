<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <!-- 引入公共的PK头部布局 -->
    <include layout="@layout/include_pk_header" />


    <!-- 自定义多人PK进度条 -->
    <hb.monitor.jvmti.widget.MultiPKProgressBar
        android:id="@+id/progressBar"
        android:layout_width="0dp"
        android:layout_height="6dp"
        android:layout_marginStart="4dp"
        android:layout_marginTop="9dp"
        android:layout_marginEnd="4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle" />

    <!-- 中央计时器 -->
    <TextView
        android:id="@+id/tvTimer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="#66000000"
        android:paddingStart="12dp"
        android:paddingTop="4dp"
        android:paddingEnd="12dp"
        android:paddingBottom="4dp"
        android:text="未开始 45:00"
        android:textColor="@android:color/white"
        android:textSize="12sp"
        app:layout_constraintEnd_toEndOf="@id/progressBar"
        app:layout_constraintStart_toStartOf="@id/progressBar"
        app:layout_constraintTop_toBottomOf="@id/progressBar" />


    <include
        android:id="@+id/collapsedContent"
        layout="@layout/view_team_pk_side_collapsed"
        android:layout_width="0dp"
        android:layout_height="48dp"
        android:layout_marginTop="24dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/progressBar"
        app:layout_constraintStart_toStartOf="@id/progressBar"
        app:layout_constraintTop_toBottomOf="@id/progressBar" />


    <include
        android:id="@+id/expandedContent"
        layout="@layout/view_team_pk_side_expanded"
        android:layout_width="0dp"
        android:layout_height="206dp"
        android:layout_marginTop="24dp"
        android:background="@drawable/bg_multiplayer_team"
        android:visibility="visible"
        app:layout_constraintEnd_toEndOf="@id/progressBar"
        app:layout_constraintStart_toStartOf="@id/progressBar"
        app:layout_constraintTop_toBottomOf="@id/progressBar"
        tools:visibility="visible" />


    <!-- 创建一个不可见的引导视图，用于统一约束 -->
    <View
        android:id="@+id/contentBottomGuide"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/expandedContent" />

    <View
        android:id="@+id/contentBottomGuideCollapsed"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/collapsedContent" />

    <Button
        android:id="@+id/btnToggleExpand"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="收起"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/contentBottomGuide" />

</merge> 