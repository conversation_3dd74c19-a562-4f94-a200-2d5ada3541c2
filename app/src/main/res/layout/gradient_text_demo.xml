<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/gradient_text_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center">

    <hb.monitor.weiget.GradientTextView
        android:id="@+id/tvCombo"
        android:textSize="40dp"
        android:textStyle="bold|italic"
        android:textColor="#11111111"
        app:gradientStartColor="#FF4336"
        app:gradientEndColor="#FFC005"
        app:strokeColor="@color/white"
        android:background="@color/TH_Blue300"
        app:strokeWidth="1dp"
        android:text="x991"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingStart="10dp"
        android:paddingEnd="10dp"/>

</LinearLayout> 