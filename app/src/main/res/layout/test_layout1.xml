<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <View
        android:id="@+id/test1"
        android:layout_width="200dp"
        android:layout_height="100dp"
        android:layout_marginBottom="12dp"
        android:background="@color/TH_Blue100_Normal"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/test2"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.0"
        app:layout_constraintVertical_chainStyle="packed" />


    <View
        android:id="@+id/test2"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/test1"
        android:background="@color/TH_Blue100_Normal"
        app:layout_constraintBottom_toTopOf="@id/test3"
        android:layout_marginBottom="8dp"
        android:visibility="gone"
        android:layout_width="200dp"
        android:layout_height="100dp"/>

    <View
        android:id="@+id/test3"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/test2"
        android:background="@color/TH_Blue100_Normal"
        app:layout_constraintBottom_toTopOf="@id/test4"
        android:layout_marginBottom="8dp"
        android:visibility="gone"
        android:layout_width="200dp"
        android:layout_height="100dp"/>


    <View
        android:id="@+id/test4"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/test3"
        android:background="@color/TH_Blue100_Normal"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="200dp"
        android:layout_height="100dp"/>
</androidx.constraintlayout.widget.ConstraintLayout>