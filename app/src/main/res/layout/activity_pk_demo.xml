<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".PKDemoActivity">

    <RadioGroup
        android:id="@+id/rg_pk_modes"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal">

        <RadioButton
            android:id="@+id/rb_team_pk"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="组队PK" />

        <RadioButton
            android:id="@+id/rb_multiplayer_pk"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:text="多人PK(混战)" />
    </RadioGroup>

    <!-- PK视图容器，用于按需加载PK视图 -->
    <FrameLayout
        android:id="@+id/pkContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"/>

    <!-- PK测试操作按钮区域 -->
    <LinearLayout
        android:id="@+id/llTestButtons"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:orientation="horizontal"
        android:visibility="gone">

        <Button
            android:id="@+id/btnExtendEvent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:text="延长活动" />

        <Button
            android:id="@+id/btnTestUpdate"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:text="测试更新" />

        <Button
            android:id="@+id/btnBatchUpdate"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:text="批量更新" />

    </LinearLayout>

    <!-- 分数测试按钮区域 -->
    <LinearLayout
        android:id="@+id/llScoreTestButtons"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:orientation="horizontal"
        android:gravity="center"
        android:visibility="gone">

        <Button
            android:id="@+id/btnBlueScore"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:text="蓝方+1分"
            android:backgroundTint="#2196F3" />

        <Button
            android:id="@+id/btnRedScore"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:text="红方+1分"
            android:backgroundTint="#F44336" />

        <Button
            android:id="@+id/btnStatusChange"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:text="状态切换"
            android:backgroundTint="#FF9800" />

        <Button
            android:id="@+id/btnModeSwitch"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="切换模式"
            android:backgroundTint="#9C27B0" />

    </LinearLayout>

    <!-- PK状态提示 -->
    <TextView
        android:id="@+id/tvPkStatus"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="8dp"
        android:text="PK进行中，实时更新排名"
        android:textColor="@android:color/white"
        android:visibility="gone" />

</LinearLayout>