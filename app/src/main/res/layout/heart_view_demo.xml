<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="心形动画示例"
        android:textSize="20sp"
        android:layout_marginBottom="16dp"/>

    <hb.monitor.weiget.HeartView
        android:id="@+id/heart_view"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_gravity="center" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="点击或长按爱心查看效果"
        android:textSize="16sp"/>

</LinearLayout> 