<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <!-- 引入公共的PK头部布局 -->
    <include layout="@layout/include_pk_header" />

    <!-- 自定义组队PK进度条 -->
    <hb.monitor.jvmti.widget.TeamPKProgressBar
        android:id="@+id/progressBar"
        android:layout_width="match_parent"
        android:layout_height="16dp"
        android:layout_marginStart="4dp"
        android:layout_marginTop="9dp"
        android:layout_marginEnd="4dp"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle" />

    <!-- 左侧分数区域 -->
    <ImageView
        android:id="@+id/ivStarLeft"
        android:layout_width="10dp"
        android:layout_height="10dp"
        android:layout_marginStart="6.5dp"
        android:src="@android:drawable/btn_star_big_on"
        app:layout_constraintBottom_toBottomOf="@id/progressBar"
        app:layout_constraintStart_toStartOf="@id/progressBar"
        app:layout_constraintTop_toTopOf="@id/progressBar" />

    <TextView
        android:id="@+id/tvScoreLeft"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="2dp"
        android:text="0"
        android:textColor="@color/TH_White100"
        android:textSize="10sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/progressBar"
        app:layout_constraintStart_toEndOf="@id/ivStarLeft"
        app:layout_constraintTop_toTopOf="@id/progressBar" />


    <!-- 右侧分数区域 -->
    <ImageView
        android:id="@+id/ivStarRight"
        android:layout_width="10dp"
        android:layout_height="10dp"
        android:layout_marginEnd="6.5dp"
        android:src="@android:drawable/btn_star_big_on"
        app:layout_constraintBottom_toBottomOf="@id/progressBar"
        app:layout_constraintEnd_toEndOf="@id/progressBar"
        app:layout_constraintTop_toTopOf="@id/progressBar" />

    <TextView
        android:id="@+id/tvScoreRight"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="2dp"
        android:text="0"
        android:textColor="@color/TH_White100"
        android:textSize="10sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/progressBar"
        app:layout_constraintEnd_toStartOf="@id/ivStarRight"
        app:layout_constraintTop_toTopOf="@id/progressBar" />


    <!-- 中央计时器（未开始时显示） -->
    <TextView
        android:id="@+id/tvTimer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="#66000000"
        android:paddingStart="12dp"
        android:paddingTop="4dp"
        android:paddingEnd="12dp"
        android:paddingBottom="4dp"
        android:text="未开始 45:00"
        android:textColor="@android:color/white"
        android:textSize="12sp"
        app:layout_constraintEnd_toEndOf="@id/progressBar"
        app:layout_constraintStart_toStartOf="@id/progressBar"
        app:layout_constraintTop_toBottomOf="@id/progressBar" />

    <!-- 中央闪电占位视图（进行中时显示） -->
    <View
        android:id="@+id/viewLightning"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:background="#FFFF00"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/progressBar"
        app:layout_constraintStart_toStartOf="@id/progressBar"
        app:layout_constraintTop_toTopOf="@id/progressBar"
        tools:visibility="visible" />


    <TextView
        android:id="@+id/tvBlueTeam"
        style="@style/Text.N3.White"
        android:layout_width="40dp"
        android:layout_height="16dp"
        android:layout_marginTop="8dp"
        android:background="@drawable/hichat_pk_blue_team_title_ic"
        android:gravity="center_vertical"
        android:paddingStart="8dp"
        android:text="蓝方"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="@id/progressBar"
        app:layout_constraintTop_toBottomOf="@id/progressBar" />

    <TextView
        android:id="@+id/tvRedTeam"
        style="@style/Text.N3.White"
        android:layout_width="40dp"
        android:layout_height="16dp"
        android:layout_marginTop="8dp"
        android:background="@drawable/hichat_pk_red_team_title_ic"
        android:gravity="center_vertical|end"
        android:paddingEnd="8dp"
        android:text="红方"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="@id/progressBar"
        app:layout_constraintTop_toBottomOf="@id/progressBar" />


    <!-- 收起状态的团队视图 -->
    <include
        android:id="@+id/blueTeamCollapsed"
        layout="@layout/view_team_pk_side_collapsed"
        android:layout_width="0dp"
        android:layout_height="36dp"
        android:visibility="gone"
        app:layout_constraintEnd_toStartOf="@+id/redTeamCollapsed"
        app:layout_constraintHorizontal_chainStyle="spread"
        app:layout_constraintStart_toStartOf="@id/progressBar"
        app:layout_constraintTop_toBottomOf="@id/tvBlueTeam" />

    <include
        android:id="@+id/redTeamCollapsed"
        layout="@layout/view_team_pk_side_collapsed"
        android:layout_width="0dp"
        android:layout_height="36dp"
        android:layout_marginStart="4dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/progressBar"
        app:layout_constraintStart_toEndOf="@+id/blueTeamCollapsed"
        app:layout_constraintTop_toBottomOf="@id/tvRedTeam" />

    <!-- 展开状态的团队视图 -->
    <include
        android:id="@+id/blueTeamExpanded"
        layout="@layout/view_team_pk_side_expanded"
        android:layout_width="0dp"
        android:layout_height="194dp"
        android:visibility="visible"
        app:layout_constraintEnd_toStartOf="@+id/redTeamExpanded"
        app:layout_constraintHorizontal_chainStyle="spread"
        app:layout_constraintStart_toStartOf="@id/progressBar"
        app:layout_constraintTop_toBottomOf="@id/tvBlueTeam"
        tools:visibility="visible" />

    <include
        android:id="@+id/redTeamExpanded"
        layout="@layout/view_team_pk_side_expanded"
        android:layout_width="0dp"
        android:layout_height="194dp"
        android:layout_marginStart="4dp"
        android:visibility="visible"
        app:layout_constraintEnd_toEndOf="@id/progressBar"
        app:layout_constraintStart_toEndOf="@+id/blueTeamExpanded"
        app:layout_constraintTop_toBottomOf="@id/tvRedTeam"
        tools:visibility="visible" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/bottomBarrier"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="bottom"
        app:constraint_referenced_ids="blueTeamCollapsed,redTeamCollapsed,blueTeamExpanded,redTeamExpanded" />

    <Button
        android:id="@+id/btnToggleExpand"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="收起"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/bottomBarrier" />

    <TextView
        android:id="@+id/tvBottomTip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="PK进行中，实时更新排名"
        android:textColor="@android:color/white"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/btnToggleExpand" />

</merge> 