<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        app:layout_constraintTop_toTopOf="parent"
        android:id="@+id/tvUsePrefix"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        app:layout_constraintEnd_toStartOf="@id/ivUseAvatar"
        app:layout_constraintStart_toStartOf="parent"
        tools:text="正在跟踪："
        app:layout_constraintHorizontal_chainStyle="packed"
        android:layout_marginStart="10dp"
        />

    <hb.ximage.fresco.BaseDraweeView
        android:id="@+id/ivUseAvatar"
        android:layout_width="16dp"
        android:layout_height="16dp"
        app:layout_constraintBottom_toBottomOf="@id/tvUsePrefix"
        app:layout_constraintStart_toEndOf="@id/tvUsePrefix"
        app:layout_constraintTop_toTopOf="@id/tvUsePrefix"
        app:layout_constraintEnd_toStartOf="@id/tvUseSuffix"
        app:roundAsCircle="true" />

    <TextView
        android:id="@+id/tvUseSuffix"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constrainedWidth="true"
        app:layout_constraintStart_toEndOf="@id/ivUseAvatar"
        android:layout_marginStart="2dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvUsePrefix"
        app:layout_constraintBottom_toBottomOf="@id/tvUsePrefix"
        android:maxLines="1"
        android:ellipsize="end"
        tools:text="昵称昵称昵称昵称昵称昵称昵称昵称昵称昵称昵称昵称昵称昵称昵称昵称昵称昵称昵称昵称昵称"
        android:layout_marginEnd="10dp"
        />

</androidx.constraintlayout.widget.ConstraintLayout>