<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/transparent"
        app:elevation="0dp"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:id="@+id/collapsingToolbarLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_scrollFlags="scroll|exitUntilCollapsed"
            android:minHeight="?attr/actionBarSize"
            app:contentScrim="@android:color/transparent" 
            app:statusBarScrim="@android:color/transparent">

            <FrameLayout
                android:id="@+id/galleryContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                >
                
                <androidx.viewpager2.widget.ViewPager2
                    android:id="@+id/galleryViewPager"
                    android:layout_width="match_parent"
                    android:layout_height="256dp"
                    android:clipChildren="false"
                    android:clipToPadding="false"
                    android:paddingStart="100dp"
                    android:paddingEnd="100dp"
                    android:layout_marginTop="?attr/actionBarSize" />

                <hb.monitor.weiget.CurveIndicatorView
                    android:id="@+id/curveIndicator"
                    android:layout_width="match_parent"
                    android:layout_height="150dp"
                    android:layout_gravity="bottom"
                    android:layout_marginBottom="30dp"
                    android:layout_marginTop="?attr/actionBarSize" /> 
            </FrameLayout>

            <LinearLayout
                android:id="@+id/customHeaderLayout"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                app:layout_collapseMode="pin"
                android:background="@android:color/transparent">

                <ImageButton
                    android:id="@+id/backButton"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:src="@drawable/ic_arrow_back"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp" />

                <TextView
                    android:id="@+id/toolbarTitle"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="wrap_content"
                    android:text="Gallery Demo"
                    android:textColor="@android:color/white"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:gravity="center" />

                <ImageButton
                    android:id="@+id/menuButton"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:src="@drawable/ic_menu"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp" />
            </LinearLayout>

        </com.google.android.material.appbar.CollapsingToolbarLayout>

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/detailsRecyclerView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior"
        android:clipToPadding="false"
        android:paddingTop="8dp"
        android:paddingBottom="8dp" />

</androidx.coordinatorlayout.widget.CoordinatorLayout> 