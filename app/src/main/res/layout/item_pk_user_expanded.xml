<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="36dp"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingEnd="4dp">

    <hb.ximage.fresco.AvatarDraweeView
        android:id="@+id/ivAvatar"
        android:layout_width="22dp"
        android:layout_height="22dp"
        android:layout_marginStart="4dp"/>

    <TextView
        android:id="@+id/tvName"
        style="@style/Text.N3.White"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:text="User Name" />

</LinearLayout> 