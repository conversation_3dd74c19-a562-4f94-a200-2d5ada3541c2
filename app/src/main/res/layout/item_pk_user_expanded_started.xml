<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="36dp"
    tools:background="#33000000">

    <TextView
        android:id="@+id/tvRank"
        style="@style/Text.N3.White"
        android:layout_width="12dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="2dp"
        android:gravity="center"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="1" />

    <View
        android:id="@+id/lottieVoice"
        android:layout_width="32dp"
        android:layout_height="32dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/ivAvatar"
        app:layout_constraintStart_toStartOf="@id/ivAvatar"
        app:layout_constraintTop_toTopOf="parent" />

    <hb.ximage.fresco.AvatarDraweeView
        android:id="@+id/ivAvatar"
        android:layout_width="22dp"
        android:layout_height="22dp"
        android:layout_marginStart="4dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvRank"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvName"
        style="@style/Text.N3.White"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:layout_marginTop="0.5dp"
        android:ellipsize="end"
        android:maxLines="1"
        app:layout_constraintEnd_toStartOf="@id/ivLock"
        app:layout_constraintStart_toEndOf="@id/ivAvatar"
        app:layout_constraintTop_toTopOf="@id/ivAvatar"
        tools:text="昵称昵称" />

    <ImageView
        android:id="@+id/ivStar"
        android:layout_width="8dp"
        android:layout_height="8dp"
        android:src="@android:drawable/btn_star_big_on"
        app:layout_constraintBottom_toBottomOf="@id/tvScore"
        app:layout_constraintStart_toStartOf="@id/tvName"
        app:layout_constraintTop_toTopOf="@id/tvScore" />

    <TextView
        android:id="@+id/tvScore"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="2dp"
        android:textColor="@color/TH_White100"
        android:textSize="8sp"
        app:layout_constraintEnd_toEndOf="@id/tvName"
        app:layout_constraintStart_toEndOf="@id/ivStar"
        app:layout_constraintTop_toBottomOf="@id/tvName"

        tools:text="9999" />

    <ImageView
        android:id="@+id/ivLock"
        android:layout_width="16dp"
        android:layout_height="20dp"
        android:src="@android:drawable/ic_lock_lock"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout> 