<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#cc000000"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <hb.monitor.demo.FamilyActiveBoxProgress
        android:id="@+id/familyProgress"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginHorizontal="20dp"
        android:layout_marginTop="20dp"
        android:layout_width="0dp"
        android:layout_height="wrap_content"/>
    
    <hb.ximage.fresco.BaseDraweeView
        android:id="@+id/draweeView"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/familyProgress"
        android:layout_marginTop="20dp"
        android:layout_width="36dp"
        android:layout_height="12dp"/>

</androidx.constraintlayout.widget.ConstraintLayout>