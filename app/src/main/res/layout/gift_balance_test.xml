<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <Space
        android:id="@+id/spaceAvailable"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginEnd="6dp"
        app:layout_constraintEnd_toStartOf="@id/viewSendBtn"
        app:layout_constraintStart_toStartOf="@id/groupQuBalance"
        app:layout_constraintTop_toTopOf="@id/groupQuBalance" />

    <View
        android:id="@+id/viewSendBtn"
        android:layout_width="120dp"
        android:layout_height="34dp"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="8dp"
        android:background="@color/TH_Yellow600"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/groupQuBalance"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="12dp"
        android:background="@color/TH_Blue200"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_goneMarginEnd="6dp"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="@id/viewSendBtn"
        app:layout_constraintEnd_toStartOf="@id/groupShellBalance"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/viewSendBtn"
        app:layout_constraintWidth_default="wrap"
        app:layout_constraintWidth_min="83dp">

        <View
            android:id="@+id/v1"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:layout_marginStart="8dp"
            android:background="@color/TH_Green800"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <TextView
            android:id="@+id/tvQuBalance"
            style="@style/Text.B2"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginStart="2dp"
            android:ellipsize="end"
            android:gravity="center"
            android:singleLine="true"
            android:text="0999999"
            android:textColor="#FFBE09"
            app:layout_constraintStart_toEndOf="@id/v1"
            app:layout_constraintEnd_toStartOf="@id/v2"
            app:layout_constraintWidth_default="wrap"/>

        <View
            android:id="@+id/v2"
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:layout_marginStart="2dp"
            android:layout_marginTop="6.5dp"
            android:layout_marginBottom="6.5dp"
            app:layout_constraintStart_toEndOf="@id/tvQuBalance"
            android:background="@color/TH_Gray250_Normal"
            app:layout_constraintEnd_toStartOf="@id/tv1"/>

        <TextView
            android:id="@+id/tv1"
            style="@style/Text.B2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="12dp"
            android:text="充值"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:textColor="#FF2056"
            android:textStyle="bold" />

    </androidx.constraintlayout.widget.ConstraintLayout>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/groupShellBalance"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="4dp"
        android:layout_marginEnd="6dp"
        android:background="@color/TH_Red100"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="@id/viewSendBtn"
        app:layout_constraintEnd_toStartOf="@id/viewSendBtn"
        app:layout_constraintStart_toEndOf="@id/groupQuBalance"
        app:layout_constraintTop_toTopOf="@id/viewSendBtn"
        app:layout_constraintWidth_default="wrap"
        app:layout_constraintWidth_min="65dp">

        <View
            android:id="@+id/v3"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:layout_marginStart="8dp"
            android:background="@color/TH_Green800"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>


        <TextView
            android:id="@+id/tvShellBalance"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginStart="2dp"
            android:ellipsize="end"
            android:gravity="center"
            android:singleLine="true"
            android:text="0"
            android:layout_marginEnd="2dp"
            app:layout_constraintStart_toEndOf="@id/v3"
            app:layout_constraintEnd_toStartOf="@id/v4"
            app:layout_constraintWidth_default="wrap"
            android:textColor="#FFBE09" />

        <View
            android:id="@+id/v4"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_marginEnd="8dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:background="@color/TH_Green800" />

    </androidx.constraintlayout.widget.ConstraintLayout>


    <EditText
        android:id="@+id/edInput"
        android:layout_width="200dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="200dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <Button
        android:id="@+id/btnInput1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="填充到1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/edInput" />


    <Button
        android:id="@+id/btnInput2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:text="填充到2"
        app:layout_constraintStart_toEndOf="@id/btnInput1"
        app:layout_constraintTop_toBottomOf="@id/edInput" />


   <Button
       android:id="@+id/btnVisible"
       android:layout_width="wrap_content"
       android:layout_height="wrap_content"
       app:layout_constraintStart_toEndOf="@id/btnInput2"
       app:layout_constraintTop_toTopOf="@id/btnInput2"
       android:layout_marginStart="4dp"
       android:text="显示隐藏贝壳"
       />

</androidx.constraintlayout.widget.ConstraintLayout>