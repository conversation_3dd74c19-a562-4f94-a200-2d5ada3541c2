<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="16dp"
    android:layout_marginEnd="16dp"
    android:padding="12dp"
    android:background="@drawable/notification_background">

    <!-- I will also create the drawable background in the next step -->

    <ImageView
        android:id="@+id/iv_notification_icon"
        android:layout_width="56dp"
        android:layout_height="56dp"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@android:drawable/sym_def_app_icon" />

    <TextView
        android:id="@+id/tv_notification_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:textColor="#333333"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/btn_notification_action"
        app:layout_constraintStart_toEndOf="@id/iv_notification_icon"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="你预约的畅聊圈节目开始啦～快去围观！" />

    <Button
        android:id="@+id/btn_notification_action"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:layout_marginEnd="8dp"
        android:background="@drawable/notification_button_background"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:textColor="#333333"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/iv_notification_close"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="去围观" />

    <ImageView
        android:id="@+id/iv_notification_close"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:padding="4dp"
        android:src="@android:drawable/ic_menu_close_clear_cancel"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout> 