<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:background="@color/black_alpha_50"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <!-- 只提取真正公共的部分：标题、帮助按钮、开始按钮、时间设置按钮 -->
    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="9dp"
        android:textColor="@color/TH_White100"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="组队" />

    <ImageView
        android:id="@+id/ivHelp"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_marginStart="8dp"
        android:src="@drawable/hichat_pk_help_ic"
        app:layout_constraintBottom_toBottomOf="@id/tvTitle"
        app:layout_constraintStart_toEndOf="@id/tvTitle"
        app:layout_constraintTop_toTopOf="@id/tvTitle" />

    <hb.drawable.shape.view.HbTextView
        android:id="@+id/btnStart"
        style="@style/Text.N1.B100"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="4dp"
        android:gravity="center"
        android:paddingStart="12dp"
        android:paddingTop="4.5dp"
        android:paddingEnd="12dp"
        android:paddingBottom="4.5dp"
        android:text="开始"
        android:textStyle="bold"
        app:corner="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:solid="@color/TH_Yellow600_Normal" />

    <hb.drawable.shape.view.HbTextView
        android:id="@+id/btnTimeSettings"
        style="@style/Text.N1.White"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="8dp"
        android:paddingStart="12dp"
        android:paddingTop="4.5dp"
        android:paddingEnd="12dp"
        android:paddingBottom="4.5dp"
        android:text="时间设置"
        android:textStyle="bold"
        app:corner="16dp"
        app:layout_constraintEnd_toStartOf="@id/btnStart"
        app:layout_constraintTop_toTopOf="@id/btnStart"
        app:solid="@color/TH_White15" />


</merge>
