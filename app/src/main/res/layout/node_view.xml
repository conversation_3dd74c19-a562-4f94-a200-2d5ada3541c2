<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginStart="5dp"
    android:layout_marginEnd="5dp"
    android:orientation="vertical">

    <View
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:background="#eeaacc" />

    <View
        android:id="@+id/anchorView"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="4dp"
        android:background="#eeaacc" />

    <TextView
        android:id="@+id/tvValue"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="2dp"
        android:textColor="@color/white"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

</LinearLayout>