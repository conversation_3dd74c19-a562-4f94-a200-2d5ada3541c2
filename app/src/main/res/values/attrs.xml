<?xml version="1.0" encoding="utf-8"?>
<resources>

    <declare-styleable name="MaxLimitRecyclerView">
        <attr name="maxHeight" format="dimension" />
        <attr name="maxWidth" format="dimension" />
    </declare-styleable>

    <declare-styleable name="GradientTextView">
        <attr name="gradientStartColor" format="color" />
        <attr name="gradientEndColor" format="color" />
        <attr name="strokeColor" format="color" />
        <attr name="strokeWidth" format="dimension" />
        <attr name="useGradient" format="boolean" />
        <attr name="useStroke" format="boolean" />
        <attr name="shadowRadius" format="dimension" />
        <attr name="shadowDx" format="dimension" />
        <attr name="shadowDy" format="dimension" />
        <attr name="shadowColor" format="color" />
    </declare-styleable>
</resources>