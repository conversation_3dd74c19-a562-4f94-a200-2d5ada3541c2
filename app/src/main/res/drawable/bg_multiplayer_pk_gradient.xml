<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 边框渐变层 -->
    <item>
        <shape>
            <gradient
                android:angle="315"
                android:startColor="#FF5DBE"
                android:endColor="#42C0FF"
                android:type="linear" />
            <!-- 12dp圆角 -->
            <corners android:radius="12dp" />
        </shape>
    </item>

    <!-- 内容背景层，留出1dp边框 -->
    <item
        android:left="1dp"
        android:top="1dp"
        android:right="1dp"
        android:bottom="1dp">
        <shape>
            <gradient
                android:angle="315"
                android:startColor="#B2460031"
                android:endColor="#B2000631"
                android:type="linear" />
            <!-- 内层圆角稍小，保持边框效果 -->
            <corners android:radius="11dp" />
        </shape>
    </item>
    
</layer-list>
