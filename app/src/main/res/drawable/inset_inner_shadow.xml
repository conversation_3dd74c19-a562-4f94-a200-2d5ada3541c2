<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 背景色和圆角 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#1E264F" />
            <corners android:radius="10dp" />
        </shape>
    </item>
    <!-- 顶部内阴影 -->
    <item>
        <shape android:shape="rectangle">
            <corners android:radius="10dp" />
            <gradient
                android:startColor="#2C345A"
                android:endColor="@android:color/transparent"
                android:angle="90"
                android:type="linear" />
        </shape>
    </item>
    <!-- 底部内阴影 -->
    <item>
        <shape android:shape="rectangle">
            <corners android:radius="10dp" />
            <gradient
                android:startColor="@android:color/transparent"
                android:endColor="#2C345A75"
                android:angle="270"
                android:type="linear" />
        </shape>
    </item>
</layer-list> 