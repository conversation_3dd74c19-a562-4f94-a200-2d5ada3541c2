plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
}

android {
    compileSdk 32

    defaultConfig {
        applicationId "hb.monitor.jvmti"
        minSdk 24
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
    viewBinding {
        enabled = true
    }
}

dependencies {
    implementation("cn.taqu.library:okhttp-util:2.3.8") {
        exclude group: 'androidx'
    }

    implementation ("hb:xcommon:5.5.1"){
        exclude group: 'hb:ximage-fresco'
        exclude group: 'hb:xstyle'
        exclude group: 'hb:drawable'
    }
    implementation 'com.google.android:flexbox:2.0.1'
    implementation "hb:utils:1.5.1"
    implementation "com.squareup.okhttp3:okhttp:3.14.10"
    implementation 'androidx.core:core-ktx:1.3.2'
    implementation 'androidx.appcompat:appcompat:1.4.2'
    implementation 'com.google.android.material:material:1.6.1'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.transition:transition-ktx:1.4.1'
//    implementation project(':compose-test')
//    implementation project(path: ':jvmti-native')
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.4'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.0'
    api "hb:xthread-pool:1.0.9"
    api "hb:kotlin_ext:1.0.4"

    // EventBus for theme change notifications
    implementation 'org.greenrobot:eventbus:3.3.1'


}