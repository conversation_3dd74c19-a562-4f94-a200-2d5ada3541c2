// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript{
    ext {
        allRepositories = {
//            maven {
//                allowInsecureProtocol true
//                url 'file://Users/<USER>/Desktop/AndroidProject/pins-plugin/maven/hb'
//            }
            // 注意：google、mavenCentral等仓库放到内网maven代理了，地址：http://android.internal.taqu.cn:8081/
            // 请勿在此引入maven仓库，要引入请通过内网maven代理。
            maven {
                allowInsecureProtocol true
                url 'http://android.internal.taqu.cn:8081/repository/android-public/'
            }
        }
    }
    dependencies {
        // pins
        classpath 'hb:pins-plugin:0.5.0-beta17'
    }
}
plugins {
    id 'com.android.application' version '7.2.0' apply false
    id 'com.android.library' version '7.2.0' apply false
    id 'org.jetbrains.kotlin.android' version '1.7.22' apply false
    id 'org.jetbrains.kotlin.jvm' version '1.7.22' apply false
}





task clean(type: Delete) {
    delete rootProject.buildDir
}